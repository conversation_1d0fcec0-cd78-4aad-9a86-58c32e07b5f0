import { Component, HostListener, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TournamentService } from '../../../services/tournament.service';
import { LoadingService } from '../../../services/loading.service';
import { Title } from '@angular/platform-browser';
import { TranslateService } from '@ngx-translate/core';
import { AutoScheduleService, UpdateLocationMatchParams } from '../../../services/auto-schedule.service';
import { CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import Swal from 'sweetalert2';
import { BreakModalParams } from "./modal-crud-break/modal-crud-break.component";
import { ToastrService } from "ngx-toastr";
import { AppConfig } from "../../../app-config";
import { StageService } from "../../../services/stage.service";
import { UpdateConfigParams } from './modal-update-config/modal-update-config.component';
import config from '../../../../../capacitor.config';


@Component({
    selector: 'app-auto-schedule',
    templateUrl: './auto-schedule.component.html',
    styleUrls: ['./auto-schedule.component.scss']
})
export class AutoScheduleComponent {

    public contentHeader: object;
    public tournamentId: any;
    public seasonId: any;
    public leagueOrGroupStageId: number | null = null;
    public tournamentInfo: null;

    public dateOptions = [];
    public listLocations = {};
    public listMatches = {};
    public listLocationIds = [];
    public responseMetadata = {};
    public responseMatches: {} | null = {}

    public teamConflicts = {};
    public refereeConflicts = {};
    public stageConflicts = {};


    public selectedConfig: UpdateConfigParams | null = null;

    public isFetching = true;
    public isLock = false;
    public hasMatches = false;


    @ViewChild('modalSetupSchedule')
    modalSetupSchedule!: TemplateRef<any>;

    @ViewChild('modalEditSchedule')
    modalEditSchedule!: TemplateRef<any>;

    @ViewChild('modalCrudBreak')
    modalCrudBreak!: TemplateRef<any>; @ViewChild('modalUpdateMatch')
    modalUpdateMatch: TemplateRef<any>;

    @ViewChild('modalRefereeList')
    modalRefereeList: TemplateRef<any>;

    // public listUnScheduledMatches = [

    // ];

    public listUnScheduledMatches = [];

    public selectedDate = null;

    public hasPlan = false;

    // Simple dropdown state - track which dropdown is open
    public activeDropdownId: string | null = null;

    // Referee expansion state
    public expandedRefereeMatches: Set<number> = new Set();
    public selectedMatchReferees: any[] = [];
    public selectedMatchId: number | null = null;

    constructor(
        public _route: ActivatedRoute,
        public _router: Router,
        public _tournamentService: TournamentService,
        public _stageService: StageService,
        public _loadingService: LoadingService,
        public _titleService: Title,
        public _translateService: TranslateService,
        public _autoScheduleService: AutoScheduleService,
        private _modalService: NgbModal,
        private _toastService: ToastrService,
    ) {
        this.isFetching = true;
        this.tournamentId = this._route.snapshot.paramMap.get('tournament_id');
        this._tournamentService.getTournament(this.tournamentId).subscribe(
            (res) => {
                this.tournamentInfo = res;

                this.leagueOrGroupStageId = res.stages.find((stage) => {
                    if (this.tournamentInfo && this.tournamentInfo['type'] === AppConfig.TOURNAMENT_TYPES.groups_knockouts) {
                        return stage.type === AppConfig.TOURNAMENT_TYPES.groups;
                    } else {
                        return stage.type === AppConfig.TOURNAMENT_TYPES.league;
                    }
                })?.id;

                this.isLock = res.is_locked_schedule === 1;
                this.seasonId = res.group.season.id;
                _titleService.setTitle(res.name);

                this.contentHeader = {
                    headerTitle: res.name,
                    actionButton: false,
                    breadcrumb: {
                        type: '',
                        links: [
                            {
                                name: this._translateService.instant('Leagues'),
                                isLink: false
                            },
                            {
                                name: this._translateService.instant('Manage Leagues'),
                                isLink: true,
                                link: '/leagues/manage'
                            },
                            {
                                name: res.name,
                                isLink: false
                            },
                            {
                                name: this._translateService.instant('Auto Schedule'),
                                isLink: false
                            }
                        ]
                    }
                };

                this.onScheduleAction(null, true)
            });
    }

    ngOnInit() {
    }

    ngAfterViewChecked() {
        setTimeout(() => {
            this.hasMatches = (this.responseMatches && Object.keys(this.responseMatches).length > 0) || this.listUnScheduledMatches.length > 0;
        }, 0)
    }

    getScheduleMatches(showLoading: boolean = true) {
        if (showLoading) {
            this._loadingService.show();
        }


        this._autoScheduleService.getScheduleMatches(this.tournamentId)
            .subscribe((res) => {
                this.responseMetadata = res.metadata;
                this.responseMatches = Array.isArray(res.data) && res.data.length === 0 ? null : res.data;

                this.teamConflicts = res.conflicts['team_scheduling_conflict'];
                this.refereeConflicts = res.conflicts['referee_scheduling_conflict'];
                this.stageConflicts = res.conflicts['stage_scheduling_conflict'];

                if (this.responseMatches) {
                    this.dateOptions = Object.keys(this.responseMatches);
                    this.selectedDate = this.selectedDate && this.dateOptions.includes(this.selectedDate)
                        ? this.selectedDate
                        : this.dateOptions[0];
                    this.mapListLocations();
                } else {
                    this.listLocationIds = [];
                    this.listMatches = {};
                    this.dateOptions = [];
                    this.selectedDate = null;
                }

                this._loadingService.dismiss();
            }, () => {
            }, () => {
                this.isFetching = false
                this.hasPlan = this.dateOptions.length > 0;
            });
    }

    getUnScheduleMatches(showLoading = true) {
        if (showLoading) {
            this._loadingService.show();
        }
        this._autoScheduleService.getListUnScheduledMatches(this.tournamentId)
            .subscribe((res) => {
                this.listUnScheduledMatches = res.data;

                this._loadingService.dismiss();
            });
    }

    mapListLocations() {
        this.listLocationIds = [];
        this.listMatches = {};

        this.listLocations = this.responseMatches[this.selectedDate] || {};

        if (!this.listLocations || !this.selectedDate) return;

        Object.keys(this.listLocations).forEach((locationName: string) => {

            if (!this.listLocationIds.includes(`${locationName}`)) {
                this.listLocationIds.push(`${locationName}`);
            }

            if (!this.listMatches[locationName]) {
                this.listMatches[locationName] = [];
            }

            this.listMatches[locationName] = [
                ...this.listMatches[locationName],
                ...this.listLocations[locationName]
            ]
        });

    }

    onSelectDate(event) {
        this.selectedDate = event;
        this.mapListLocations();
    }

    openModalSetupSchedule() {
        this._modalService.open(this.modalSetupSchedule, {
            centered: true,
            size: 'lg'
        });
    }

    onScheduleAction(response, showLoading: boolean = true) {
        this.getScheduleMatches(showLoading);
        this.getUnScheduleMatches(showLoading);
    }

    unScheduleMatch(timeSlotId: string | number, configId: string | number, onError: () => void, onSuccess: () => void) {
        this._autoScheduleService.unScheduleMatch(timeSlotId, configId).subscribe((res) => {
            this.onScheduleAction(null, false);
            onSuccess && onSuccess()
        }, (error) => {
            onError();
        })
    }


    mapNewSlotIndex(locationKey) {

        const newSlotIndex = {};
        this.listMatches[locationKey].forEach((item, index) => {

            newSlotIndex[item.time_slot_id] = index;
        });
        return newSlotIndex;
    }

    updateLocationMatch(locationKey: string, updateData: UpdateLocationMatchParams, onError: () => void, onSuccess: () => void) {
        const newIndex = this.mapNewSlotIndex(locationKey);
        this._autoScheduleService.updateLocationMatch(updateData).subscribe((res) => {
            this.onScheduleAction(null, false);
            onSuccess && onSuccess();
        }, (error) => {
            onError();
        });
    }


    drop(event: CdkDragDrop<string[]>) {
        const targetContainer = event.container.element.nativeElement;
        const prevContainer = event.previousContainer.element.nativeElement;
        const dragItem = event.item.element.nativeElement;

        const targetContainerId = targetContainer['id'];
        const prevContainerId = prevContainer['id'];

        const targetStageId = targetContainer['data_stage_id'];
        const targetLocationId = targetContainer['data_location_id'];

        const prevStageId = prevContainer['data_stage_id'];
        const prevLocationId = prevContainer['data_location_id'];

        const itemStageId = dragItem['data_stage_id'];
        const itemTimeSlotId = dragItem['data_time_slot_id'];
        const itemType = dragItem['data_type'];

        if (prevContainerId === targetContainerId && event.currentIndex === event.previousIndex) return;

        if (targetContainerId === 'unScheduleZone') {
            transferArrayItem(
                event.previousContainer.data,
                event.container.data,
                event.previousIndex,
                event.currentIndex
            );

            if (targetContainerId === prevContainerId) return;

            this.unScheduleMatch(itemTimeSlotId, this.getConfig(prevLocationId)?.id, () => {
                transferArrayItem(
                    event.container.data,
                    event.previousContainer.data,
                    event.currentIndex,
                    event.previousIndex
                );
                this._toastService.error(this._translateService.instant('Failed to unschedule match.'));
            }, () => {
                this._toastService.success(this._translateService.instant('Match unscheduled successfully.'));
            });
        } else {
            if (event.previousContainer === event.container) {
                moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
                this.updateLocationMatch(targetContainerId, {
                    new_index: this.mapNewSlotIndex(targetContainerId),
                    location_id: targetLocationId,
                    prev_location_id: prevLocationId,
                    stage_id: targetStageId,
                    prev_stage_id: prevStageId,
                    tournament_id: this.tournamentId,
                    config_id: this.getConfig(targetLocationId)?.id,
                    prev_config_id: this.getConfig(prevLocationId)?.id,
                }, () => {
                    moveItemInArray(event.container.data, event.currentIndex, event.previousIndex);
                    this._toastService.error(this._translateService.instant('Failed to update match schedule.'));
                }, () => {
                    this._toastService.success(this._translateService.instant('Match schedule updated successfully.'));
                });
            } else {
                transferArrayItem(
                    event.previousContainer.data,
                    event.container.data,
                    event.previousIndex,
                    event.currentIndex
                )

                this.updateLocationMatch(targetContainerId,
                    {
                        new_index: this.mapNewSlotIndex(targetContainerId),
                        location_id: targetLocationId,
                        prev_location_id: prevContainerId === 'unScheduleZone' ? null : prevLocationId,
                        stage_id: targetStageId,
                        prev_stage_id: prevContainerId === 'unScheduleZone' ? null : prevStageId,
                        tournament_id: this.tournamentId,
                        config_id: this.getConfig(targetLocationId)?.id,
                        prev_config_id: this.getConfig(prevLocationId)?.id,
                    },
                    () => {
                        transferArrayItem(
                            event.container.data,
                            event.previousContainer.data,
                            event.currentIndex,
                            event.previousIndex
                        );
                        this._toastService.error(this._translateService.instant('Failed to update match schedule.'));
                    }, () => {
                        this._toastService.success(this._translateService.instant('Match schedule updated successfully.'));
                    });
            }
        }
    }

    getShortTime(time: string): string {
        // handle return ISO string to short time format HH:mm and format 24 hours
        if (!time) return '';
        const date = new Date(time);
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        return `${hours}:${minutes}`;
    }

    getConfig(location_id) {
        return this.responseMetadata['configs'].find((item) => {
            return item.tournament_id === +this.tournamentId && item.location_id === +location_id && item.begin_date === this.selectedDate;
        });
    }

    editSchedule(locationKey, configId) {
        this.selectedConfig = {
            tournamentId: this.tournamentId,
            location: this.responseMetadata['locations'][locationKey].id,
            date: this.selectedDate,
            configId,
            timeSlotIds: this.listMatches[locationKey].map((item) => item.time_slot_id),
        };
        this._modalService.open(this.modalEditSchedule, {
            centered: true,
        });
    }

    deletePlan(locationKey) {

        const locationId = this.responseMetadata['locations'][locationKey].id;
        const configId = this.getConfig(locationId)?.id;
        const timeSlotIds = this.listMatches[locationKey].map((item) => item.time_slot_id);

        Swal.fire({
            title: this._translateService.instant('Are you sure?'),
            text: this._translateService.instant('You will not be able to recover this schedule!'),
            icon: 'warning',
            showCancelButton: true,
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                this._autoScheduleService.deleteSchedule(this.tournamentId, locationId, timeSlotIds, configId).subscribe((res) => {
                    this.onScheduleAction(null, false);
                    this._toastService.success(this._translateService.instant('Schedule deleted successfully.'));
                });
            }
        });
    }

    public breakModalParams: BreakModalParams = {
        locationId: null,
        tournamentId: null,
        timeSlotId: null,
        lastTimeSlotId: null,
        configId: null
    }

    openModalAddBreak(locationId, lastTimeSlotId, configId) {
        console.log("🚀 ~ AutoScheduleComponent ~ openModalAddBreak ~ configId:", configId)
        console.log("🚀 ~ AutoScheduleComponent ~ openModalAddBreak ~ lastTimeSlotId:", lastTimeSlotId)
        this.breakModalParams = {
            ...this.breakModalParams,
            locationId,
            tournamentId: this.tournamentId,
            lastTimeSlotId,
            configId
        }

        this._modalService.open(this.modalCrudBreak, {
            centered: true,

        });

    }

    // Simple dropdown methods
    toggleDropdown(event: MouseEvent, item: any): void {

        event.preventDefault();
        event.stopPropagation();

        if (event.target['className'] === 'swap-button') {
            return;
        }

        const dropdownId = this.getDropdownId(item);

        // Close dropdown if clicking on the same item
        if (this.activeDropdownId === dropdownId) {
            this.activeDropdownId = null;
        } else {
            // Open new dropdown (close any existing one)
            this.activeDropdownId = dropdownId;
        }
    }

    isDropdownOpen(item: any): boolean {
        const dropdownId = this.getDropdownId(item);
        return this.activeDropdownId === dropdownId;
    }

    getDropdownId(item: any): string {
        // Create unique ID for each item
        return `${item.time_slot_id}_${item.type}_${item.stage_id || 'unscheduled'}`;
    }

    closeAllDropdowns(): void {
        this.activeDropdownId = null;
    }

    @HostListener('document:click', ['$event'])
    onDocumentClick(event: MouseEvent): void {
        const target = event.target as HTMLElement;

        // Close dropdown if clicking outside of dropdown or dnd-item
        if (!target.closest('.item-dropdown') && !target.closest('.dnd-item')) {
            this.closeAllDropdowns();
        }
    }


    public selectedItem = null;

    // Dropdown action handlers
    onEditMatch(item: any): void {
        this.closeAllDropdowns();

        this.selectedItem = item;

        this._modalService.open(this.modalUpdateMatch, {
            centered: true,
        });

    }

    onUnscheduleTimeSlot(item: any, configId): void {
        const description = {
            match: 'This match will be moved to unscheduled matches.',
            break: 'This break will be removed.'
        }

        const successMessage = {
            match: 'Match unscheduled successfully.',
            break: 'Break removed successfully.'
        }

        const errorMessage = {
            match: 'Failed to unschedule match.',
            break: 'Failed to remove break.'
        }


        Swal.fire({
            title: this._translateService.instant('Are you sure?'),
            text: this._translateService.instant(description[item.type]),
            icon: 'warning',
            showCancelButton: true,
            reverseButtons: true,
            confirmButtonText: this._translateService.instant('Yes'),
            cancelButtonText: this._translateService.instant('Cancel')
        }).then((result) => {
            if (result.isConfirmed) {
                this.unScheduleMatch(item.time_slot_id, configId, () => {
                    this._toastService.error(this._translateService.instant(errorMessage[item.type]));
                }, () => {
                    this._toastService.success(this._translateService.instant(successMessage[item.type]));
                });
            }
        });

        this.closeAllDropdowns();
    }

    onEditEventTime(item: any, configId: number): void {

        this.closeAllDropdowns();

        this.breakModalParams = {
            timeSlotId: item.time_slot_id,
            locationId: item.location_id,
            tournamentId: item.tournament_id,
            description: item.description,
            breakDurations: item.break_durations,
            configId: configId,
        }

        this._modalService.open(this.modalCrudBreak, {
            centered: true,
        })

    }

    clearSchedule() {
        Swal.fire({
            title: this._translateService.instant('Are you sure?'),
            text: this._translateService.instant('This action will clear all schedule.'),
            icon: 'warning',
            showCancelButton: true,
            reverseButtons: true,
            confirmButtonText: this._translateService.instant('Yes, clear it!'),
            cancelButtonText: this._translateService.instant('Cancel')
        }).then((result) => {
            if (result.isConfirmed) {
                this._autoScheduleService.clearSchedule(this.tournamentId).subscribe((res) => {
                    Swal.fire({
                        title: this._translateService.instant('Success!'),
                        text: this._translateService.instant('Schedule has been cleared.'),
                        icon: 'success'
                    });
                    this.onScheduleAction(null, false);
                }, (error) => {
                    Swal.fire({
                        title: this._translateService.instant('Error!'),
                        text: this._translateService.instant('Schedule has been cleared.'),
                        icon: 'success'
                    });
                })
            }
        });
    }

    isMatchHasConflict(scheduleMatchId: number, type: 'team' | 'referee' | 'match', itemCheckId?: number) {
        switch (type) {
            case 'team':
                return this.teamConflicts[scheduleMatchId]?.find(item => item.team_id === itemCheckId);
            case 'referee':
                return this.refereeConflicts[scheduleMatchId]?.find(item => item.referee_id === itemCheckId);
            case 'match':
                return this.teamConflicts[scheduleMatchId] || this.refereeConflicts[scheduleMatchId];
        }
    }

    onClickLock() {
        this.isLock = !this.isLock;
        this._autoScheduleService.updateTournamentScheduleStatus(this.tournamentId).subscribe((res) => {
            this._toastService.success(res.message)
        }, (error) => {

            this.isLock = !this.isLock;
            this._toastService.error(error.message)
        });
    }

    swapTeam(matchInfo) {
        this.closeAllDropdowns();
        this._stageService.swapTeams(matchInfo).subscribe((res) => {
            this._toastService.success('Swap teams successfully.');
            this.onScheduleAction(null, false);
        }, (error) => {
            this._toastService.error(error.message || 'Failed to swap teams.');
        })
    } autoGenerate() {
        this._stageService.autoGenerateMatches(this.leagueOrGroupStageId).subscribe((res) => {
            this._toastService.success('Auto generate matches successfully.');
            this.onScheduleAction(null, false);
        }, (error) => {
            Swal.fire({
                title: 'Cannot Auto Generate',
                icon: error.warning ? 'warning' : 'error',
                text: error.warning || error.message || 'Failed to auto generate matches.',
                confirmButtonText: 'Go to Stage Details',
            }).then((result) => {
                if (result.isConfirmed) {
                    this._router.navigate(['leagues', 'manage', this.tournamentId, 'stages', this.leagueOrGroupStageId]);
                }
            });
        })
    }

    getRefereeDisplayLimit(referees: any[], matchId: number): number {
        if (this.expandedRefereeMatches.has(matchId)) {
            return referees.length; // Show all if expanded
        }

        // Calculate average name length
        const totalNameLength = referees.reduce((sum, ref) => {
            const name = ref.referee_type === 'user'
                ? `${ref.user?.first_name || ''} ${ref.user?.last_name || ''}`.trim()
                : ref.referee_name || '';
            return sum + name.length;
        }, 0);

        const averageLength = totalNameLength / referees.length;

        // If average name length is long (>15 chars), show 3; otherwise show 4
        return averageLength > 15 ? 3 : 4;
    }

    getDisplayedReferees(referees: any[], matchId: number): any[] {
        if (!referees || referees.length === 0) {
            return [];
        }

        const limit = this.getRefereeDisplayLimit(referees, matchId);
        return referees.slice(0, limit);
    }

    getRemainingRefereesCount(referees: any[], matchId: number): number {
        if (!referees || referees.length === 0 || this.expandedRefereeMatches.has(matchId)) {
            return 0;
        }

        const limit = this.getRefereeDisplayLimit(referees, matchId);
        return Math.max(0, referees.length - limit);
    }

    toggleRefereeExpansion(matchId: number, event: Event): void {
        event.stopPropagation();
        event.preventDefault();

        const matchItem = this.findMatchById(matchId);
        if (!matchItem || !matchItem.referees || matchItem.referees.length === 0) {
            return;
        }


        this.showRefereeModal(matchItem.referees, matchId);
    }

    showRefereeModal(referees: any[], matchId: number): void {
        this.selectedMatchReferees = referees;
        this.selectedMatchId = matchId;

        this._modalService.open(this.modalRefereeList, {
            centered: true,
            size: 'md'
        });
    }

    findMatchById(matchId: number): any {
        for (const locationKey of this.listLocationIds) {
            const matches = this.listMatches[locationKey] || [];
            const match = matches.find(item => item.id === matchId);
            if (match) {
                return match;
            }
        }

        // Also check unscheduled matches
        return this.listUnScheduledMatches.find(item => item.id === matchId) || null;
    }

    protected readonly location = location;

}
