(function(angular) {
  'use strict';
angular.module('FilterInControllerModule', []).
  controller('FilterController', ['filterFilter', function FilterController(filterFilter) {
    this.array = [
      {name: '<PERSON>'},
      {name: '<PERSON>'},
      {name: '<PERSON>'},
      {name: '<PERSON>'},
      {name: '<PERSON>'},
      {name: '<PERSON>'}
    ];
    this.filteredArray = filterFilter(this.array, 'a');
  }]);
})(window.angular);