<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Example - example-date-input-directive-debug</title>
  

  <script src="../../../angular.js"></script>
  

  
</head>
<body ng-app="dateInputExample">
  <script>
   angular.module('dateInputExample', [])
     .controller('DateController', ['$scope', function($scope) {
       $scope.example = {
         value: new Date(2013, 9, 22)
       };
     }]);
</script>
<form name="myForm" ng-controller="DateController as dateCtrl">
   <label for="exampleInput">Pick a date in 2013:</label>
   <input type="date" id="exampleInput" name="input" ng-model="example.value"
       placeholder="yyyy-MM-dd" min="2013-01-01" max="2013-12-31" required />
   <div role="alert">
     <span class="error" ng-show="myForm.input.$error.required">
         Required!</span>
     <span class="error" ng-show="myForm.input.$error.date">
         Not a valid date!</span>
    </div>
    <tt>value = {{example.value | date: "yyyy-MM-dd"}}</tt><br/>
    <tt>myForm.input.$valid = {{myForm.input.$valid}}</tt><br/>
    <tt>myForm.input.$error = {{myForm.input.$error}}</tt><br/>
    <tt>myForm.$valid = {{myForm.$valid}}</tt><br/>
    <tt>myForm.$error.required = {{!!myForm.$error.required}}</tt><br/>
</form>
</body>
</html>