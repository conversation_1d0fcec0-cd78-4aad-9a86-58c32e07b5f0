<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Example - example-controller-spicy-2-jquery</title>
  

  <script src="../../components/jquery-3.2.1/jquery.js"></script>
  <script src="../../../angular.js"></script>
  <script src="app.js"></script>
  

  
</head>
<body ng-app="spicyApp2">
  <div ng-controller="SpicyController">
 <input ng-model="customSpice">
 <button ng-click="spicy('chili')">Chili</button>
 <button ng-click="spicy(customSpice)">Custom spice</button>
 <p>The food is {{spice}} spicy!</p>
</div>
</body>
</html>