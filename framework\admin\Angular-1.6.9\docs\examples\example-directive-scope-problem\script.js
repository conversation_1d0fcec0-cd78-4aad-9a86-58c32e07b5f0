(function(angular) {
  'use strict';
angular.module('docsScopeProblemExample', [])
  .controller('<PERSON><PERSON><PERSON>roll<PERSON>', ['$scope', function($scope) {
    $scope.customer = {
      name: '<PERSON>',
      address: '1600 Amphitheatre'
    };
  }])
  .controller('<PERSON><PERSON><PERSON>roller', ['$scope', function($scope) {
    $scope.customer = {
      name: '<PERSON>',
      address: '123 Somewhere'
    };
  }])
  .directive('myCustomer', function() {
    return {
      restrict: 'E',
      templateUrl: 'my-customer.html'
    };
  });
})(window.angular);