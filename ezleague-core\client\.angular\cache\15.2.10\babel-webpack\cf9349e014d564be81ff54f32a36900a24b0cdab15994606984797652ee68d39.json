{"ast": null, "code": "export default function decodeHtmlText(encoded) {\n  const txt = document.createElement('textarea');\n  txt.innerHTML = encoded;\n  return txt.value;\n}", "map": {"version": 3, "mappings": "AAAA,eAAc,SAAUA,cAAc,CAACC,OAAe;EAClD,MAAMC,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;EAC9CF,GAAG,CAACG,SAAS,GAAGJ,OAAO;EACvB,OAAOC,GAAG,CAACI,KAAK;AACpB", "names": ["decodeHtmlText", "encoded", "txt", "document", "createElement", "innerHTML", "value"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\helpers\\decode-html-special-chars.ts"], "sourcesContent": ["export default function decodeHtmlText(encoded: string): string {\r\n    const txt = document.createElement('textarea');\r\n    txt.innerHTML = encoded;\r\n    return txt.value;\r\n}"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}