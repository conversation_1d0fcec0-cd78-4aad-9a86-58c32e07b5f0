(function(angular) {
  'use strict';
angular.module('docsIsolationExample', [])
  .controller('Controller', ['$scope', function($scope) {
    $scope.naomi = { name: '<PERSON>', address: '1600 Amphitheatre' };
    $scope.vojta = { name: '<PERSON>oj<PERSON>', address: '3456 Somewhere Else' };
  }])
  .directive('myCustomer', function() {
    return {
      restrict: 'E',
      scope: {
        customerInfo: '=info'
      },
      templateUrl: 'my-customer-plus-vojta.html'
    };
  });
})(window.angular);