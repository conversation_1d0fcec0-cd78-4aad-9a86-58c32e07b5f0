<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Example - example-datetimelocal-input-directive-debug</title>
  

  <script src="../../../angular.js"></script>
  

  
</head>
<body ng-app="dateExample">
  <script>
  angular.module('dateExample', [])
    .controller('DateController', ['$scope', function($scope) {
      $scope.example = {
        value: new Date(2010, 11, 28, 14, 57)
      };
    }]);
</script>
<form name="myForm" ng-controller="DateController as dateCtrl">
  <label for="exampleInput">Pick a date between in 2013:</label>
  <input type="datetime-local" id="exampleInput" name="input" ng-model="example.value"
      placeholder="yyyy-MM-ddTHH:mm:ss" min="2001-01-01T00:00:00" max="2013-12-31T00:00:00" required />
  <div role="alert">
    <span class="error" ng-show="myForm.input.$error.required">
        Required!</span>
    <span class="error" ng-show="myForm.input.$error.datetimelocal">
        Not a valid date!</span>
  </div>
  <tt>value = {{example.value | date: "yyyy-MM-ddTHH:mm:ss"}}</tt><br/>
  <tt>myForm.input.$valid = {{myForm.input.$valid}}</tt><br/>
  <tt>myForm.input.$error = {{myForm.input.$error}}</tt><br/>
  <tt>myForm.$valid = {{myForm.$valid}}</tt><br/>
  <tt>myForm.$error.required = {{!!myForm.$error.required}}</tt><br/>
</form>
</body>
</html>