(function(angular) {
  'use strict';
var myApp = angular.module('scopeInheritance', []);
myApp.controller('MainController', ['$scope', function($scope) {
  $scope.timeOfDay = 'morning';
  $scope.name = '<PERSON>';
}]);
myApp.controller('ChildController', ['$scope', function($scope) {
  $scope.name = '<PERSON>ie';
}]);
myApp.controller('GrandChildController', ['$scope', function($scope) {
  $scope.timeOfDay = 'evening';
  $scope.name = 'Gingerbread Baby';
}]);
})(window.angular);