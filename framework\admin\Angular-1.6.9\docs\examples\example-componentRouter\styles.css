h1 {color: #369; font-family: Arial, Helvetica, sans-serif; font-size: 250%;}
h2 { color: #369; font-family: Arial, Helvetica, sans-serif;  }
h3 { color: #444; font-weight: lighter; }
body { margin: 2em; }
body, input[text], button { color: #888; font-family: Cambria, Georgia; }
button {padding: 0.2em; font-size: 14px}

ul {list-style-type: none; margin-left: 1em; padding: 0; width: 20em;}

li { cursor: pointer; position: relative; left: 0; transition: all 0.2s ease; }
li:hover {color: #369; background-color: #EEE; left: .2em;}

/* route-link anchor tags */
a {padding: 5px; text-decoration: none; font-family: Arial, Helvetica, sans-serif; }
a:visited, a:link {color: #444;}
a:hover {color: white; background-color: #1171a3; }
a.router-link-active {color: white; background-color: #52b9e9; }

.selected { background-color: #EEE; color: #369; }

.badge {
  font-size: small;
  color: white;
  padding: 0.1em 0.7em;
  background-color: #369;
  line-height: 1em;
  position: relative;
  left: -1px;
  top: -1px;
}

crisis-detail input {
  width: 20em;
}