<?php
namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class ScheduleTimeSlot extends Model
{
    protected $fillable = [
        'tournament_id',
        'stage_id',
        'location_id',
        'start_time',
        'end_time',
        'slot_index',
        'type',
    ];

    protected $dates = ['start_time', 'end_time'];

    /**
     * Always store times in UTC and convert on retrieval
     * This ensures consistent timezone handling across the application
     */
    public function setAttribute($key, $value)
    {
        // For time fields, ensure we store in UTC
        if (in_array($key, ['start_time', 'end_time']) && is_string($value)) {
            if (preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $value)) {
                // Parse as UTC if no timezone context is available
                $value = Carbon::parse($value, 'UTC')->utc()->format('Y-m-d H:i:s');
            }
        }
        return parent::setAttribute($key, $value);
    }

    /**
     * Convert times from UTC to request timezone on retrieval
     */
    public function getAttribute($key)
    {
        $value = parent::getAttribute($key);

        // For time fields, convert from UTC to request timezone
        if (in_array($key, ['start_time', 'end_time']) && is_string($value)) {
            if (preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $value)) {
                $timezone = request()->hasHeader('X-time-zone') ? request()->header('X-time-zone') : config('app.timezone');
                $value = Carbon::parse($value, 'UTC')->setTimezone($timezone);
            }
        }

        return $value;
    }

    public function tournament(): BelongsTo
    {
        return $this->belongsTo(Tournament::class);
    }

    public function stage(): BelongsTo
    {
        return $this->belongsTo(Stage::class);
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function scheduleMatch(): HasOne
    {
        return $this->hasOne(ScheduleMatch::class, 'time_slot_id');
    }

    public function break(): HasOne
    {
        return $this->hasOne(ScheduleBreak::class, 'time_slot_id');
    }
}
