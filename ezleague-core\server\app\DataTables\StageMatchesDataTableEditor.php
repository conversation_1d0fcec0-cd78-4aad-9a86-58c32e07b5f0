<?php

namespace App\DataTables;

use Illuminate\Http\Request;
use App\Http\Controllers\ScheduleTimeSlotController;
use App\Http\Controllers\StageController;
use App\Models\Rankings;
use App\Models\ScheduleMatch;
use App\Models\ScheduleTimeSlot;
use App\Models\Stage;
use App\Models\StageMatch;
use App\Models\User;
use App\Rules\EndTimeAfterStartTime;
use App\Rules\MatchLabelRule;
use App\Rules\MatchTeamRule;
use App\Rules\Rules\KnockoutScore;
use App\Rules\NotEqualRule;
use Carbon\Carbon;
use DateTimeZone;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Validation\Rule;
use Yajra\DataTables\DataTablesEditor;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class StageMatchesDataTableEditor extends DataTablesEditor
{
    protected $model = StageMatch::class;

    /**
     * Get create action validation rules.
     */
    public function createRules(): array
    {
        $data = request()->get('data');
        // get first value of array
        $data = array_values($data)[0];
        // find stage by stage_id
        $match_id = isset($data['match_id']) ? $data['match_id'] : 0;
        $request = request();
        $stage_type = isset($request['stage_type']) ? $request['stage_type'] : config('constants.tournament_types.league');
        return [
            'stage_id' => 'required|integer',
            'home_team_id' => ['integer', 'nullable', 'exists:teams,id', new MatchTeamRule($data, $match_id, $stage_type)],
            'away_team_id' => ['integer', 'nullable', 'exists:teams,id', new NotEqualRule($data, 'home_team_id'), new MatchTeamRule($data, $match_id, $stage_type)],
            'date' => ['sometimes', 'nullable', 'date_format:Y-m-d', 'after_or_equal:1970-01-01', 'before_or_equal:2038-01-19'],
            'start_time' => ['sometimes', 'nullable'],
            'end_time' => ['sometimes', 'nullable', new EndTimeAfterStartTime($data['start_time'] ?? null)],
            'start_time_short' => ['sometimes', 'nullable'],
            'end_time_short' => ['sometimes', 'nullable', new EndTimeAfterStartTime($data['start_time_short'] ?? null)],
            'location_id' => 'sometimes|integer|nullable|exists:locations,id',
            'round_name' => 'sometimes|string',
            'round_level' => 'sometimes|string',
            'home_score' => [
                'sometimes',
                'integer',
                'nullable'
                // new KnockoutScore($model,'away_score'),
            ],
            'away_score' => [
                'sometimes',
                'integer',
                'nullable'
                // new KnockoutScore($model,'home_score'),
            ],
            'home_penalty' => 'sometimes|integer|nullable',
            'away_penalty' => 'sometimes|integer|nullable',
            'status' => 'sometimes|string',
            'description' => 'sometimes|string|nullable',
        ];
    }

    /**
     * Get edit action validation rules.
     * @param Model $model
     * @return array
     */
    public function editRules(Model $model): array
    {
        $data = request()->get('data');

        // get first value of array
        $data = array_values($data)[0];
        $request = request();
        $stage_type = isset($request['stage_type']) ? $request['stage_type'] : config('constants.tournament_types.league');
        return [
            'stage_id' => 'sometimes|required|integer',
            'home_team_id' => ['sometimes', 'integer', 'nullable', 'exists:teams,id', new MatchTeamRule($data, $model->id, $stage_type)],
            'away_team_id' => ['sometimes', 'integer', 'nullable', 'exists:teams,id', new NotEqualRule($data, 'home_team_id'), new MatchTeamRule($data, $model->id, $stage_type)],
            'date' => ['sometimes', 'nullable', 'date_format:Y-m-d', 'after_or_equal:1970-01-01', 'before_or_equal:2038-01-19'],
            'start_time' => ['sometimes', 'nullable'],
            'end_time' => ['sometimes', 'nullable', new EndTimeAfterStartTime(isset($data['start_time']) ? $data['start_time'] : null)],
            'start_time_short' => ['sometimes', 'nullable'],
            'end_time_short' => ['sometimes', 'nullable', new EndTimeAfterStartTime($data['start_time_short'] ?? null)],
            'location_id' => 'sometimes|integer|nullable|exists:locations,id',
            'round_name' => 'sometimes|string',
            'round_level' => 'sometimes|string',
            'home_label_id' => ['sometimes', 'integer', 'nullable', 'exists:rankings,id', new MatchLabelRule($data, $model->id, $stage_type, 'home_label_id')],
            'away_label_id' => [
                'sometimes',
                'integer',
                'nullable',
                'exists:rankings,id',
                function ($attribute, $value, $fail) use ($data) {
                    $homeLabelId = $data['home_label_id'] ?? null;
                    if ($homeLabelId && $homeLabelId == $value) {
                        return $fail(__("Home label and away label cannot be the same"));
                    }
                },
                new MatchLabelRule($data, $model->id, $stage_type, 'away_label_id')
            ],
            'home_score' => [
                'sometimes',
                'integer',
                'nullable'
                // new KnockoutScore($model,'away_score'),
            ],
            'away_score' => [
                'sometimes',
                'integer',
                'nullable'
                // new KnockoutScore($model,'home_score'),
            ],
            'home_penalty' => ['sometimes', 'integer', 'nullable'],
            'away_penalty' => [
                'sometimes',
                'integer',
                'nullable',
                function ($attribute, $value, $fail) use ($model, $data) {
                    $newHomePenalty = $data['home_penalty'] ?? null;
                    $newAwayPenalty = $data['away_penalty'] ?? null;
                    if ($newHomePenalty == $newAwayPenalty) {
                        return $fail(__("Home penalty and away penalty cannot be equal"));
                    }
                }
            ],
            'status' => 'sometimes|string',
            'description' => 'sometimes|string|nullable'
        ];
    }

    // updating
    public function updating(Model $model, array $data): array
    {
        return $data;
    }

    /**
     * Get remove action validation rules.
     */
    public function removeRules(Model $model): array
    {
        return [];
    }

    /**
     * Event hook that is fired before deleting an existing record.
     * Implements cascading delete for related ScheduleMatch and ScheduleTimeSlot records.
     */
    public function deleting(Model $model, array $data): void
    {
        try {
            $scheduleMatch = ScheduleMatch::where('match_id', $model->id)->first();

            if ($scheduleMatch) {
                ScheduleTimeSlot::where('id', $scheduleMatch->time_slot_id)->delete();
                $scheduleMatch->delete();
            }
        } catch (Exception $e) {
            Log::error("Error during cascading delete for StageMatch ID: {$model->id}. Error: " . $e->getMessage());
            throw $e; // Re-throw the exception to prevent the deletion
        }
    }

    /**
     * Event hook that is fired after deleting the record from database.
     * Used for logging and verification that the deletion was successful.
     */
    public function deleted(Model $model, array $data): void
    {

    }

    /**
     * Event hook that is fired after `creating` and `updating` hooks, but before
     * the model is saved to the database.
     */
    public function saving(Model $model, array $data): array
    {
        // get type_action in request
        $type_action = request()->get('type_action');
        $stage_type = request()->get('stage_type');

        Log::info('$type_action', [$type_action]);
        if ($type_action == 'cancelMatch') {

            $scheduleMatch = ScheduleMatch::where('match_id', $model->id)->first();

            if ($scheduleMatch) {
                ScheduleTimeSlot::find($scheduleMatch->time_slot_id)->update([
                    'location_id' => null,
                    'start_time' => null,
                    'end_time' => null,
                ]);
            }
        }


        if (isset($data['start_time_short'])) {
            $data['start_time'] = is_null($data['start_time_short']) ? null : date('Y-m-d H:i:s', strtotime($data['date'] . ' ' . $data['start_time_short']));
        } else if (isset($data['date']) && !isset($data['end_time_short'])) {
            $data['start_time'] = date('Y-m-d H:i:s', strtotime($data['date']));
        } else if (isset($data['date']) && isset($data['end_time_short'])) {
            $data['start_time'] = null;
        }

        if (isset($data['end_time_short'])) {
            $data['end_time'] = is_null($data['end_time_short']) ? null : date('Y-m-d H:i:s', strtotime($data['date'] . ' ' . $data['end_time_short']));
        } else if (isset($data['date']) && !isset($data['start_time_short'])) {
            $data['end_time'] = date('Y-m-d H:i:s', strtotime($data['date']));
        } else if (isset($data['date']) && isset($data['start_time_short'])) {
            $data['end_time'] = null;
        }

        if (isset($data['round_level'])) {
            $round_level = $data['round_level'];
            // check stage type is league
            if ($stage_type == config('constants.tournament_types.league')) {
                // check if round_level is number
                if (is_numeric($round_level)) {
                    if ($round_level == 0) {
                        $data['round_name'] = 'Friendly';
                    } else if ($round_level > 0) {
                        $data['round_name'] = 'Round ' . $round_level;
                    }
                }
            }
        }
        if ($stage_type != config('constants.tournament_types.league')) {
            //log stage_type
            Log::info('Stage type: ', [$stage_type]);
            if (isset($data['home_label_id']) && isset($data['away_label_id'])) {
                $homeLabel = Rankings::find($data['home_label_id']);
                $awayLabel = Rankings::find($data['away_label_id']);

                if ($homeLabel && isset($homeLabel->team_id)) {
                    $data['home_team_id'] = $homeLabel->team_id;
                }

                if ($awayLabel && isset($awayLabel->team_id)) {
                    $data['away_team_id'] = $awayLabel->team_id;
                }
            } else {
                // log $data
                Log::info('Data before home_team_id and away_team_id', [$data]);

                if (isset($data['home_team_id']) && isset($data['away_team_id'])) {
                    $homeLabelId = Rankings::where([
                        'team_id' => $data['home_team_id'],
                        'stage_id' => $data['stage_id']
                    ])->first();
                    $awayLabelId = Rankings::where([
                        'team_id' => $data['away_team_id'],
                        'stage_id' => $data['stage_id']
                    ])->first();
                    Log::info('$homeLabelId $awayLabelId', [$homeLabelId, $awayLabelId]);

                    $data['home_label_id'] = $homeLabelId ? $homeLabelId->id : null;
                    $data['away_label_id'] = $awayLabelId ? $awayLabelId->id : null;
                }
            }
        }
        return $data;
    }

    /**
     * Event hook that is fired after `created` and `updated` events.
     */
    public function saved(Model $model, array $data): Model
    { //    get team in match

        $scheduleMatch = ScheduleMatch::where('match_id', $model->id)
            ->with('timeSlot')->first();

        $type_action = request()->get('type_action');

        if ($type_action == 'addReplaceMatch') {
            $scheduleTimeSlotController = new ScheduleTimeSlotController();


            // create new time slot
            $scheduleTimeSlotController->manualAddTimeSlot(
                $model->id,
                $model->stage->tournament_id,
                $model->location_id,
                $model->start_time,
                $model->end_time,
                $model->stage_id,
                // or any logic to determine index
            );
        }

        if (isset($scheduleMatch) && isset($model->start_time) && isset($model->end_time) && $type_action != 'cancelMatch') {
            $startTimeUtc = Carbon::parse($model->start_time, config('app.timezone'))->setTimezone('UTC')->format('Y-m-d H:i:s');
            $endTimeUtc = Carbon::parse($model->end_time, config('app.timezone'))->setTimezone('UTC')->format('Y-m-d H:i:s');
            $scheduleMatch->timeSlot->start_time = $startTimeUtc;
            $scheduleMatch->timeSlot->end_time = $endTimeUtc;
            $scheduleMatch->timeSlot->location_id = $model->location_id;
            $scheduleMatch->timeSlot->save();
        }

        $home_team = $model->homeTeam;
        $away_team = $model->awayTeam;
        if ($home_team != null) {
            //get club manager of team
            $home_club_manager = $home_team->club->users;
            // get user favoutite team
            $home_team_favourite = $home_team->usersFavoriteTeams;
            // get user favoutite club
            $home_club_favourite = $home_team->club->usersFavoriteClubs;
            // merge and unique user
            $home_users = $home_club_manager->merge($home_team_favourite)->merge($home_club_favourite)->unique('id');
        }
        if ($away_team != null) {
            $away_club_manager = $away_team->club->users;
            $away_team_favourite = $away_team->usersFavoriteTeams;
            $away_club_favourite = $away_team->club->usersFavoriteClubs;
            $away_users = $away_club_manager->merge($away_team_favourite)->merge($away_club_favourite)->unique('id');
        }

        if (isset($data['status']) && in_array($model->status, config('constants.cancel_match_types'))) {
            if (isset($home_users)) {
                foreach ($home_users as $user) {
                    // Log::info('home: ', [$user->firebase_token]);
                    if ($user->firebase_token) {
                        $user->sendNotiCancelMatch($model);
                    }
                }
            }

            if (isset($away_team)) {
                foreach ($away_users as $user) {
                    // Log::info('away: ', [$user->firebase_token]);
                    if ($user->firebase_token) {
                        $user->sendNotiCancelMatch($model);
                    }
                }
            }
        }

        // If there is a score update but no penalty update
        if (isset($data['home_score']) && isset($data['away_score']) && !isset($data['home_penalty']) && !isset($data['away_penalty'])) {
            // Update the model with the new value
            $model->update([
                'home_penalty' => null,
                'away_penalty' => null,
            ]);
        }

        // if update score
        if (isset($data['home_score']) || isset($data['away_score']) || isset($data['home_penalty']) || isset($data['away_penalty'])) {
            // if match is finished
            $is_penalty = false;
            if (!in_array($model->status, config('constants.cancel_match_types')) || $model->status == 'null' || $model->status == null) {
                if (isset($data['home_penalty']) || isset($data['away_penalty'])) {
                    $is_penalty = true;
                }

                if (isset($home_users)) {
                    foreach ($home_users as $user) {
                        // Log::info($user->firebase_token);
                        if ($user->firebase_token) {
                            $user->sendNotiUpdateScore($model, $is_penalty);
                        }
                    }
                }

                if (isset($away_team)) {
                    foreach ($away_users as $user) {
                        // Log::info($user->firebase_token);
                        if ($user->firebase_token) {
                            $user->sendNotiUpdateScore($model, $is_penalty);
                        }
                    }
                }
            }
        }
        return $model;
    }

    protected function toJson($data, array $errors = [], $error = '')
    {
        $timezone = request()->header('X-Time-Zone') ?? '+00:00';
        $newtimeZone = new DateTimeZone($timezone);
        $code = 200;
        $stageCtrl = new StageController();
        // for each data
        $count = count($data);
        for ($key = 0; $key < $count; $key++) {
            $first_data = $data[$key]->with(['homeTeam', 'awayTeam', 'location', 'homeLabel', 'awayLabel', 'referees'])->where('id', $data[$key]->id)->first();
            if ((isset($first_data['home_score']) && isset($first_data['away_score'])) || (isset($first_data['home_penalty']) && isset($first_data['away_penalty'])) || isset($first_data['status']) && $first_data['status'] == 'pass') {
                $match_result = $stageCtrl->calculateNextMatchKnockOut($first_data);
                $winner_matches = isset($match_result['winner_matches']) ? $match_result['winner_matches'] : null;

                $loser_matches = isset($match_result['loser_matches']) ? $match_result['loser_matches'] : null;

                // add winner match to the last of the data
                if ($winner_matches) {
                    foreach ($winner_matches as $winner_match) {
                        $data[] = $winner_match;
                        $count++;
                    }
                }
                // add loser match to the last of the data
                if ($loser_matches) {
                    foreach ($loser_matches as $loser_match) {
                        $data[] = $loser_match;
                        $count++;
                    }
                }
            }
            $first_data = $first_data ? $first_data : $data[$key];
            // add date column
            if (is_null($first_data->start_time)) {
                if (is_null($first_data->end_time)) {
                    $first_data->date = 'TBD';
                } else {
                    $date = Carbon::parse($first_data->end_time, config('app.timezone'))->setTimezone($newtimeZone);
                    $first_data->date = date('Y-m-d', strtotime($date));
                }
            } else {
                $date = Carbon::parse($first_data->start_time, config('app.timezone'))->setTimezone($newtimeZone);
                $first_data->date = date('Y-m-d', strtotime($date));
            }
            // set start_time_short column to TBD if start_time is null or  start_time == end_time
            if (is_null($first_data->start_time) || (date('H:i:s', strtotime($first_data->start_time)) == date('H:i:s', strtotime($first_data->end_time)))) {
                $first_data->start_time_short = 'TBD';
            } else {
                $start_time_short = new \DateTime($first_data->start_time);
                $first_data->start_time_short = $start_time_short->format('H:i');
            }
            // edit end_time column
            if (is_null($first_data->end_time) || (date('H:i:s', strtotime($first_data->start_time)) == date('H:i:s', strtotime($first_data->end_time)))) {
                $first_data->end_time_short = 'TBD';
            } else {
                $end_time_short = new \DateTime($first_data->end_time);
                $first_data->end_time_short = $end_time_short->format('H:i');
            }
            // edit location name column
            if (is_null($first_data->location)) {
                $first_data->location_name = 'TBD';
            } else {
                $first_data->location_name = $first_data->location->name;
            }
            // // edit home_team name column
            if (is_null($first_data->homeTeam)) {
                $first_data->home_team_name = 'TBD';
            } else {
                $first_data->home_team_name = $first_data->homeTeam->name;
            }
            // edit away_team name column
            if (is_null($first_data->awayTeam)) {
                $first_data->away_team_name = 'TBD';
            } else {
                $first_data->away_team_name = $first_data->awayTeam->name;
            }
            // edit home_score column
            $first_data->home_score = is_null($first_data->home_score) ? '' : $first_data->home_score;
            // edit away_score column
            $first_data->away_score = is_null($first_data->away_score) ? '' : $first_data->away_score;
            // add group_round column
            $first_data->group_round = trim(explode('-', $first_data->round_name)[0]);

            $data[$key] = $first_data;
            // Log::info('data', [$data]);
        }


        $response = [
            'action' => $this->action,
            'data' => $data,
        ];


        if ($error) {
            $code = 422;
            $response['error'] = $error;
        }

        if ($errors) {

            $code = 422;
            $response['fieldErrors'] = $errors;
        }
        return new JsonResponse($response, $code);
    }
}
