(function(angular) {
  'use strict';
angular.module('anchoringExample', ['ngAnimate', 'ngRoute'])
  .config(['$routeProvider', function($routeProvider) {
    $routeProvider.when('/', {
      templateUrl: 'home.html',
      controller: 'HomeController as home'
    });
    $routeProvider.when('/profile/:id', {
      templateUrl: 'profile.html',
      controller: 'ProfileController as profile'
    });
  }])
  .run(['$rootScope', function($rootScope) {
    $rootScope.records = [
      { id: 1, title: 'Miss Be<PERSON>h Roob' },
      { id: 2, title: '<PERSON> Mo<PERSON>sette' },
      { id: 3, title: 'Miss Ava Pouros' },
      { id: 4, title: '<PERSON>' },
      { id: 5, title: '<PERSON>' },
      { id: 6, title: '<PERSON>' },
      { id: 7, title: '<PERSON><PERSON><PERSON>' },
      { id: 8, title: '<PERSON><PERSON>' },
      { id: 9, title: '<PERSON><PERSON><PERSON>' },
      { id: 10, title: '<PERSON><PERSON>' }
    ];
  }])
  .controller('HomeController', [function() {
    //empty
  }])
  .controller('ProfileController', ['$rootScope', '$routeParams',
      function ProfileController($rootScope, $routeParams) {
    var index = parseInt($routeParams.id, 10);
    var record = $rootScope.records[index - 1];

    this.title = record.title;
    this.id = record.id;
  }]);
})(window.angular);