import { Component, EventEmitter, Input, Output } from '@angular/core';
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { FormGroup } from "@angular/forms";
import { FormlyFieldConfig } from "@ngx-formly/core";
import { TranslateService } from "@ngx-translate/core";
import { AutoScheduleService } from "../../../../services/auto-schedule.service";
import { LoadingService } from "../../../../services/loading.service";
import Swal from 'sweetalert2';

export type BreakModalParams = {
    locationId: number | null;
    tournamentId: number | null;
    timeSlotId?: number | null;
    description?: string | null;
    breakDurations?: number | null;
    lastTimeSlotId?: number | null
    configId?: number | null
}

@Component({
    selector: 'app-modal-crud-break',
    templateUrl: './modal-crud-break.component.html',
    styleUrls: ['./modal-crud-break.component.scss']
})


export class ModalCrudBreakComponent {

    @Input() breakModalParams: BreakModalParams = {
        locationId: null,
        tournamentId: null,
        timeSlotId: null,
        lastTimeSlotId: null,
    };

    @Output() onSubmit = new EventEmitter();

    addBreakForm = new FormGroup({});
    addBreakModel = {};
    addBreakFields: FormlyFieldConfig[] = [
        {
            key: 'tournament_id',
            type: 'input',
            props: {
                type: 'hidden'
            }
        },
        {
            key: 'location_id',
            type: 'input',
            props: {
                type: 'hidden'
            }
        },
        {
            key: 'description',
            type: 'input',
            props: {
                label: 'Event name',
                placeholder: 'Enter event name (Default: Break)',
                required: false,
                type: 'text',
                maxLength: 100
            },
            defaultValue: "Break",
            // validate for it
            validation: {
                messages: {
                    maxLength: this._translateService.instant('Event name must be less than 100 characters.')
                }
            }
        },
        {
            key: 'break_durations',
            type: 'input',
            props: {
                label: 'Break duration',
                placeholder: 'Enter break duration (in minutes)',
                required: true,
                type: 'number',
                min: 1,
                max: 1440,
            },
            defaultValue: 30,
            validation: {
                messages: {
                    required: this._translateService.instant('Break duration is required.'),
                    min: this._translateService.instant('Break duration must be at least 1 minute.'),
                    max: this._translateService.instant('Break duration must be less than 1 day (1440 minutes).')
                }
            }
        },
        {
            key: 'time_slot_id',
            type: 'input',
            props: {
                type: 'hidden'
            }
        },
        {
            key: 'last_time_slot_id',
            type: 'input',
            props: {
                type: 'hidden'
            }
        }
        ,
        {
            key: 'config_id',
            type: 'input',
            props: {
                type: 'hidden'
            }
        }

    ];

    constructor(
        private _modalService: NgbModal,
        private _translateService: TranslateService,
        private _autoSchedule: AutoScheduleService,
        private _loadingService: LoadingService,
    ) {

    }

    ngOnInit() {
        this.addBreakFields[0].defaultValue = this.breakModalParams.tournamentId;
        this.addBreakFields[1].defaultValue = this.breakModalParams.locationId;
        this.addBreakFields[5].defaultValue = this.breakModalParams.lastTimeSlotId;
        this.addBreakFields[6].defaultValue = this.breakModalParams.configId;
        if (this.breakModalParams.timeSlotId) {
            this.addBreakFields[2].defaultValue = this.breakModalParams.description;
            this.addBreakFields[3].defaultValue = this.breakModalParams.breakDurations;
            this.addBreakFields[4].defaultValue = this.breakModalParams.timeSlotId;

        }
    }

    onSubmitCrudBreak(model) {
        console.log(model);
        // return;
        this._loadingService.show();

        const action = this.breakModalParams.timeSlotId
            ? this._autoSchedule.updateBreak(model)
            : this._autoSchedule.addBreak(model);

        action.subscribe({
            next: (res) => {
                console.log('res', res);
                if (this.breakModalParams.timeSlotId) {
                    Swal.fire({
                        title: this._translateService.instant('Success!'),
                        text: this._translateService.instant('Break updated successfully'),
                        icon: 'success'
                    });
                }
                this.onSubmit.emit(res);
                this._modalService.dismissAll();
            },
            error: (error) => {
                console.error(
                    this.breakModalParams.timeSlotId
                        ? 'Error updating break:'
                        : 'Error adding break:',
                    error
                );
            },
            complete: () => {
                this._loadingService.dismiss();
            }
        });
    }

    closeModal() {
        this.addBreakModel = {};
        this._modalService.dismissAll();
    }

    clearForm() {
        this.addBreakForm.reset();
    }
}
