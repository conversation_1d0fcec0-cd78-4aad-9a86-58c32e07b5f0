<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Example - example-forms-debounce-production</title>
  

  <script src="//code.angularjs.org/1.6.9/angular.min.js"></script>
  <script src="script.js"></script>
  

  
</head>
<body ng-app="debounceExample">
  <div ng-controller="ExampleController">
  <form>
    <label>Name:
    <input type="text" ng-model="user.name" ng-model-options="{ debounce: 250 }" /></label><br />
  </form>
  <pre>username = "{{user.name}}"</pre>
</div>
</body>
</html>