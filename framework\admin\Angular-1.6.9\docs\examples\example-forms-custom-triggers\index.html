<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Example - example-forms-custom-triggers</title>
  

  <script src="../../../angular.min.js"></script>
  <script src="script.js"></script>
  

  
</head>
<body ng-app="customTriggerExample">
  <div ng-controller="ExampleController">
  <form>
    <label>Name:
      <input type="text" ng-model="user.name" ng-model-options="{ updateOn: 'blur' }" /></label><br />
    <label>
    Other data:
    <input type="text" ng-model="user.data" /></label><br />
  </form>
  <pre>username = "{{user.name}}"</pre>
  <pre>userdata = "{{user.data}}"</pre>
</div>
</body>
</html>