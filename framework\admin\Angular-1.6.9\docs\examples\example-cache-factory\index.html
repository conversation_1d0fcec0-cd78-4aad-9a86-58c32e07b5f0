<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Example - example-cache-factory</title>
  <link href="style.css" rel="stylesheet" type="text/css">
  

  <script src="../../../angular.min.js"></script>
  <script src="script.js"></script>
  

  
</head>
<body ng-app="cacheExampleApp">
  <div ng-controller="CacheController">
  <input ng-model="newCacheKey" placeholder="Key">
  <input ng-model="newCacheValue" placeholder="Value">
  <button ng-click="put(newCacheKey, newCacheValue)">Cache</button>

  <p ng-if="keys.length">Cached Values</p>
  <div ng-repeat="key in keys">
    <span ng-bind="key"></span>
    <span>: </span>
    <b ng-bind="cache.get(key)"></b>
  </div>

  <p>Cache Info</p>
  <div ng-repeat="(key, value) in cache.info()">
    <span ng-bind="key"></span>
    <span>: </span>
    <b ng-bind="value"></b>
  </div>
</div>
</body>
</html>