<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Example - example-currency-filter-production</title>
  

  <script src="//code.angularjs.org/1.6.9/angular.min.js"></script>
  

  
</head>
<body ng-app="currencyExample">
  <script>
  angular.module('currencyExample', [])
    .controller('ExampleController', ['$scope', function($scope) {
      $scope.amount = 1234.56;
    }]);
</script>
<div ng-controller="ExampleController">
  <input type="number" ng-model="amount" aria-label="amount"> <br>
  default currency symbol ($): <span id="currency-default">{{amount | currency}}</span><br>
  custom currency identifier (USD$): <span id="currency-custom">{{amount | currency:"USD$"}}</span><br>
  no fractions (0): <span id="currency-no-fractions">{{amount | currency:"USD$":0}}</span>
</div>
</body>
</html>