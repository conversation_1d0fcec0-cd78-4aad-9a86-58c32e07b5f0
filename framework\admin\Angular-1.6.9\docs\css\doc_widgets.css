ul.doc-example {
  list-style-type: none;
  position: relative;
  font-size: 14px;
}

ul.doc-example > li {
  border: 2px solid gray;
  border-radius: 5px;
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  background-color: white;
  margin-bottom: 20px;
}

ul.doc-example > li.doc-example-heading {
  border: none;
  border-radius: 0;
  margin-bottom: -10px;
}

span.nojsfiddle {
  float: right;
  font-size: 14px;
  margin-right: 10px;
  margin-top: 10px;
}

form.jsfiddle {
  position: absolute;
  right: 0;
  z-index: 1;
  height: 14px;
}

form.jsfiddle button {
  cursor: pointer;
  padding: 4px 10px;
  margin: 10px;
  background-color: #FFF;
  font-weight: bold;
  color: #7989D6;
  border-color: #7989D6;
  -moz-border-radius: 8px;
  -webkit-border-radius: 8px;
  border-radius: 8px;
}

form.jsfiddle textarea, form.jsfiddle input {
  display: none;
}

li.doc-example-live {
  padding: 10px;
  font-size: 1.2em;
}

div.syntaxhighlighter {
  padding-bottom: 1px !important; /* fix to remove unnecessary scrollbars */
}

/* TABS - tutorial environment navigation */

div.tabs-nav {
  height: 25px;
  position: relative;
}

div.tabs-nav ul li {
  list-style: none;
  display: inline-block;
  padding: 5px 10px;
}

div.tabs-nav ul li.current a {
  color: white;
  text-decoration: none;
}

div.tabs-nav ul li.current {
  background: #7989D6;
  -moz-box-shadow: 4px 4px 6px #48577D;
  -moz-border-radius-topright: 8px;
  -moz-border-radius-topleft: 8px;
  box-shadow: 4px 4px 6px #48577D;
  border-radius-topright: 8px;
  border-radius-topleft: 8px;
  -webkit-box-shadow: 4px 4px 6px #48577D;
  -webkit-border-top-right-radius: 8px;
  -webkit-border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-top-left-radius: 8px;
}

div.tabs-content {
  padding: 4px;
  position: relative;
  background: #7989D6;
  -moz-border-radius: 8px;
  border-radius: 8px;
  -webkit-border-radius: 8px;
}

div.tabs-content-inner {
  margin: 1px;
  padding: 10px;
  background: white;
  border-radius: 6px;
  -moz-border-radius: 6px;
  -webkit-border-radius: 6px;
}


/* Tutorial Nav Bar */

#tutorial-nav {
  margin: 0.5em 0 1em 0;
  padding: 0;
  list-style-type: none;
  background: #7989D6;

  -moz-border-radius: 15px;
  -webkit-border-radius: 15px;
  border-radius: 15px;

  -moz-box-shadow: 4px 4px 6px #48577D;
  -webkit-box-shadow: 4px 4px 6px #48577D;
  box-shadow: 4px 4px 6px #48577D;
}


#tutorial-nav li {
  display: inline;
}


#tutorial-nav a:link, #tutorial-nav a:visited {
  font-size: 1.2em;
  color: #FFF;
  text-decoration: none;
  text-align: center;
  display: inline-block;
  width: 11em;
  padding: 0.2em 0;
}


#tutorial-nav a:hover {
  color: #000;
}
