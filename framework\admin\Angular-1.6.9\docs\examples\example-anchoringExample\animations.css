.record {
  display:block;
  font-size:20px;
}
.profile {
  background:black;
  color:white;
  font-size:100px;
}
.view-container {
  position:relative;
}
.view-container > .view.ng-animate {
  position:absolute;
  top:0;
  left:0;
  width:100%;
  min-height:500px;
}
.view.ng-enter, .view.ng-leave,
.record.ng-anchor {
  transition:0.5s linear all;
}
.view.ng-enter {
  transform:translateX(100%);
}
.view.ng-enter.ng-enter-active, .view.ng-leave {
  transform:translateX(0%);
}
.view.ng-leave.ng-leave-active {
  transform:translateX(-100%);
}
.record.ng-anchor-out {
  background:red;
}