<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Example - example-forms-async-validation-debug</title>
  

  <script src="../../../angular.js"></script>
  <script src="script.js"></script>
  

  
</head>
<body ng-app="form-example1">
  <form name="form" class="css-form" novalidate>
  <div>
    <label>
    Size (integer 0 - 10):
    <input type="number" ng-model="size" name="size"
           min="0" max="10" integer />{{size}}</label><br />
    <span ng-show="form.size.$error.integer">The value is not a valid integer!</span>
    <span ng-show="form.size.$error.min || form.size.$error.max">
      The value must be in range 0 to 10!</span>
  </div>

  <div>
    <label>
    Username:
    <input type="text" ng-model="name" name="name" username />{{name}}</label><br />
    <span ng-show="form.name.$pending.username">Checking if this name is available...</span>
    <span ng-show="form.name.$error.username">This username is already taken!</span>
  </div>

</form>
</body>
</html>