<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Example - example-forms-custom-form-controls-production</title>
  

  <script src="//code.angularjs.org/1.6.9/angular.min.js"></script>
  <script src="script.js"></script>
  

  
</head>
<body ng-app="form-example2">
  <div contentEditable="true" ng-model="content" title="Click to edit">Some</div>
<pre>model = {{content}}</pre>

<style type="text/css">
  div[contentEditable] {
    cursor: pointer;
    background-color: #D0D0D0;
  }
</style>
</body>
</html>