<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Example - example-forms-simple-debug</title>
  

  <script src="../../../angular.js"></script>
  

  
</head>
<body ng-app="formExample">
  <div ng-controller="ExampleController">
  <form novalidate class="simple-form">
    <label>Name: <input type="text" ng-model="user.name" /></label><br />
    <label>E-mail: <input type="email" ng-model="user.email" /></label><br />
    Best Editor: <label><input type="radio" ng-model="user.preference" value="vi" />vi</label>
    <label><input type="radio" ng-model="user.preference" value="emacs" />emacs</label><br />
    <input type="button" ng-click="reset()" value="Reset" />
    <input type="submit" ng-click="update(user)" value="Save" />
  </form>
  <pre>user = {{user | json}}</pre>
  <pre>master = {{master | json}}</pre>
</div>

<script>
  angular.module('formExample', [])
    .controller('ExampleController', ['$scope', function($scope) {
      $scope.master = {};

      $scope.update = function(user) {
        $scope.master = angular.copy(user);
      };

      $scope.reset = function() {
        $scope.user = angular.copy($scope.master);
      };

      $scope.reset();
    }]);
</script>
</body>
</html>