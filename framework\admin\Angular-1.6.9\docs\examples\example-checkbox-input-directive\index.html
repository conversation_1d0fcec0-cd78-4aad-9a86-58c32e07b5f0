<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Example - example-checkbox-input-directive</title>
  

  <script src="../../../angular.min.js"></script>
  

  
</head>
<body ng-app="checkboxExample">
  <script>
  angular.module('checkboxExample', [])
    .controller('ExampleController', ['$scope', function($scope) {
      $scope.checkboxModel = {
       value1 : true,
       value2 : 'YES'
     };
    }]);
</script>
<form name="myForm" ng-controller="ExampleController">
  <label>Value1:
    <input type="checkbox" ng-model="checkboxModel.value1">
  </label><br/>
  <label>Value2:
    <input type="checkbox" ng-model="checkboxModel.value2"
           ng-true-value="'YES'" ng-false-value="'NO'">
   </label><br/>
  <tt>value1 = {{checkboxModel.value1}}</tt><br/>
  <tt>value2 = {{checkboxModel.value2}}</tt><br/>
 </form>
</body>
</html>