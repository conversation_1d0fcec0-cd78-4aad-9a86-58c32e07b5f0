<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Example - example-expression-locals-jquery</title>
  

  <script src="../../components/jquery-3.2.1/jquery.js"></script>
  <script src="../../../angular.js"></script>
  <script src="script.js"></script>
  

  
</head>
<body ng-app="expressionExample">
  <div class="example2" ng-controller="ExampleController">
  Name: <input ng-model="name" type="text"/>
  <button ng-click="greet()">Greet</button>
  <button ng-click="window.alert('Should not see me')">Won't greet</button>
</div>
</body>
</html>