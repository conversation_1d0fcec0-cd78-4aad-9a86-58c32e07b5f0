<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Example - example-anchor-scroll-offset-jquery</title>
  <link href="style.css" rel="stylesheet" type="text/css">
  

  <script src="../../components/jquery-3.2.1/jquery.js"></script>
  <script src="../../../angular.js"></script>
  <script src="script.js"></script>
  

  
</head>
<body ng-app="anchorScrollOffsetExample">
  <div class="fixed-header" ng-controller="headerCtrl">
  <a href="" ng-click="gotoAnchor(x)" ng-repeat="x in [1,2,3,4,5]">
    Go to anchor {{x}}
  </a>
</div>
<div id="anchor{{x}}" class="anchor" ng-repeat="x in [1,2,3,4,5]">
  Anchor {{x}} of 5
</div>
</body>
</html>