{"ast": null, "code": "import { moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';\nimport Swal from 'sweetalert2';\nimport { AppConfig } from \"../../../app-config\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../services/tournament.service\";\nimport * as i3 from \"../../../services/stage.service\";\nimport * as i4 from \"../../../services/loading.service\";\nimport * as i5 from \"@angular/platform-browser\";\nimport * as i6 from \"@ngx-translate/core\";\nimport * as i7 from \"../../../services/auto-schedule.service\";\nimport * as i8 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i9 from \"ngx-toastr\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"@angular/flex-layout/extended\";\nimport * as i12 from \"@angular/forms\";\nimport * as i13 from \"@core/directives/core-ripple-effect/core-ripple-effect.directive\";\nimport * as i14 from \"@core/directives/core-feather-icons/core-feather-icons\";\nimport * as i15 from \"app/layout/components/content-header/content-header.component\";\nimport * as i16 from \"@ng-select/ng-select\";\nimport * as i17 from \"@angular/cdk/drag-drop\";\nimport * as i18 from \"./modal-setup-schedule/modal-setup-schedule.component\";\nimport * as i19 from \"./modal-update-config/modal-update-config.component\";\nimport * as i20 from \"./modal-crud-break/modal-crud-break.component\";\nimport * as i21 from \"./modal-update-match/modal-update-match.component\";\nconst _c0 = [\"modalSetupSchedule\"];\nconst _c1 = [\"modalEditSchedule\"];\nconst _c2 = [\"modalCrudBreak\"];\nconst _c3 = [\"modalUpdateMatch\"];\nconst _c4 = [\"modalRefereeList\"];\nfunction AutoScheduleComponent_ng_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const date_r24 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", date_r24);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", date_r24, \" \");\n  }\n}\nfunction AutoScheduleComponent_div_22_ng_container_2_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"button\", 50);\n    i0.ɵɵelement(2, \"i\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 52)(4, \"a\", 53);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_div_22_ng_container_2_div_7_Template_a_click_4_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const locationKey_r26 = i0.ɵɵnextContext().$implicit;\n      const ctx_r30 = i0.ɵɵnextContext(2);\n      let tmp_b_0;\n      return i0.ɵɵresetView(ctx_r30.editSchedule(locationKey_r26, (tmp_b_0 = ctx_r30.getConfig(ctx_r30.responseMetadata[\"locations\"][locationKey_r26].id)) == null ? null : tmp_b_0.id));\n    });\n    i0.ɵɵtext(5, \" Edit Plan \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"a\", 53);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_div_22_ng_container_2_div_7_Template_a_click_6_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const locationKey_r26 = i0.ɵɵnextContext().$implicit;\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r33.deletePlan(locationKey_r26));\n    });\n    i0.ɵɵtext(7, \" Delete Plan \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AutoScheduleComponent_div_22_ng_container_2_div_11_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 58);\n    i0.ɵɵelement(1, \"i\", 59);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AutoScheduleComponent_div_22_ng_container_2_div_11_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c5 = function (a0) {\n  return {\n    $implicit: a0\n  };\n};\nfunction AutoScheduleComponent_div_22_ng_container_2_div_11_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AutoScheduleComponent_div_22_ng_container_2_div_11_ng_container_2_ng_container_1_Template, 1, 0, \"ng-container\", 60);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r35 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵnextContext(3);\n    const _r22 = i0.ɵɵreference(53);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r22)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c5, item_r35));\n  }\n}\nfunction AutoScheduleComponent_div_22_ng_container_2_div_11_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 61)(2, \"p\", 62);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 63);\n    i0.ɵɵelement(5, \"i\", 64);\n    i0.ɵɵelementStart(6, \"p\", 65);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r35 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", item_r35.description || \"Break time\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", item_r35.break_durations, \" mins \");\n  }\n}\nfunction AutoScheduleComponent_div_22_ng_container_2_div_11_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_div_22_ng_container_2_div_11_ng_container_6_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r46);\n      const item_r35 = i0.ɵɵnextContext().$implicit;\n      const ctx_r44 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r44.onEditMatch(item_r35));\n    });\n    i0.ɵɵelement(2, \"i\", 67);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_div_22_ng_container_2_div_11_ng_container_6_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r46);\n      const item_r35 = i0.ɵɵnextContext().$implicit;\n      const locationKey_r26 = i0.ɵɵnextContext().$implicit;\n      const ctx_r47 = i0.ɵɵnextContext(2);\n      let tmp_b_0;\n      return i0.ɵɵresetView(ctx_r47.onUnscheduleTimeSlot(item_r35, (tmp_b_0 = ctx_r47.getConfig(ctx_r47.responseMetadata[\"locations\"][locationKey_r26].id)) == null ? null : tmp_b_0.id));\n    });\n    i0.ɵɵelement(6, \"i\", 68);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 2, \"Update Match Referees\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 4, \"Unschedule Match\"), \" \");\n  }\n}\nfunction AutoScheduleComponent_div_22_ng_container_2_div_11_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_div_22_ng_container_2_div_11_ng_container_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r52);\n      const item_r35 = i0.ɵɵnextContext().$implicit;\n      const locationKey_r26 = i0.ɵɵnextContext().$implicit;\n      const ctx_r50 = i0.ɵɵnextContext(2);\n      let tmp_b_0;\n      return i0.ɵɵresetView(ctx_r50.onEditEventTime(item_r35, (tmp_b_0 = ctx_r50.getConfig(ctx_r50.responseMetadata[\"locations\"][locationKey_r26].id)) == null ? null : tmp_b_0.id));\n    });\n    i0.ɵɵelement(2, \"i\", 69);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_div_22_ng_container_2_div_11_ng_container_7_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r52);\n      const item_r35 = i0.ɵɵnextContext().$implicit;\n      const locationKey_r26 = i0.ɵɵnextContext().$implicit;\n      const ctx_r54 = i0.ɵɵnextContext(2);\n      let tmp_b_0;\n      return i0.ɵɵresetView(ctx_r54.onUnscheduleTimeSlot(item_r35, (tmp_b_0 = ctx_r54.getConfig(ctx_r54.responseMetadata[\"locations\"][locationKey_r26].id)) == null ? null : tmp_b_0.id));\n    });\n    i0.ɵɵelement(6, \"i\", 68);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(4, 2, \"Edit Event Time\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(8, 4, \"Delete Event\"), \" \");\n  }\n}\nconst _c6 = function (a0) {\n  return {\n    \"conflict-border\": a0\n  };\n};\nfunction AutoScheduleComponent_div_22_ng_container_2_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r58 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_div_22_ng_container_2_div_11_Template_div_click_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r58);\n      const item_r35 = restoredCtx.$implicit;\n      const ctx_r57 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(!ctx_r57.isLock && ctx_r57.toggleDropdown($event, item_r35));\n    });\n    i0.ɵɵtemplate(1, AutoScheduleComponent_div_22_ng_container_2_div_11_button_1_Template, 2, 0, \"button\", 55);\n    i0.ɵɵtemplate(2, AutoScheduleComponent_div_22_ng_container_2_div_11_ng_container_2_Template, 2, 4, \"ng-container\", 19);\n    i0.ɵɵtemplate(3, AutoScheduleComponent_div_22_ng_container_2_div_11_ng_container_3_Template, 8, 2, \"ng-container\", 19);\n    i0.ɵɵelementStart(4, \"div\", 56)(5, \"div\", 57);\n    i0.ɵɵtemplate(6, AutoScheduleComponent_div_22_ng_container_2_div_11_ng_container_6_Template, 9, 6, \"ng-container\", 19);\n    i0.ɵɵtemplate(7, AutoScheduleComponent_div_22_ng_container_2_div_11_ng_container_7_Template, 9, 6, \"ng-container\", 19);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r35 = ctx.$implicit;\n    const ctx_r28 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c6, ctx_r28.isMatchHasConflict(item_r35.id, \"match\")))(\"data_stage_id\", item_r35.stage_id)(\"data_time_slot_id\", item_r35.time_slot_id)(\"data_type\", item_r35.type)(\"cdkDragDisabled\", ctx_r28.isLock);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r28.isMatchHasConflict(item_r35.id, \"match\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r35.type === \"match\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r35.type === \"break\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"visible\", ctx_r28.isDropdownOpen(item_r35));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r35.type === \"match\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r35.type === \"break\");\n  }\n}\nfunction AutoScheduleComponent_div_22_ng_container_2_footer_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r60 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"footer\", 70)(1, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_div_22_ng_container_2_footer_12_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r60);\n      const locationKey_r26 = i0.ɵɵnextContext().$implicit;\n      const ctx_r59 = i0.ɵɵnextContext(2);\n      let tmp_b_0;\n      return i0.ɵɵresetView(ctx_r59.openModalAddBreak(ctx_r59.responseMetadata[\"locations\"][locationKey_r26].id, ctx_r59.listMatches[locationKey_r26][ctx_r59.listMatches[locationKey_r26].length - 1] == null ? null : ctx_r59.listMatches[locationKey_r26][ctx_r59.listMatches[locationKey_r26].length - 1].time_slot_id, (tmp_b_0 = ctx_r59.getConfig(ctx_r59.responseMetadata[\"locations\"][locationKey_r26].id)) == null ? null : tmp_b_0.id));\n    });\n    i0.ɵɵelement(2, \"i\", 72);\n    i0.ɵɵtext(3, \" Add event / break \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AutoScheduleComponent_div_22_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r63 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 40)(2, \"div\", 41)(3, \"header\", 23)(4, \"div\", 42)(5, \"p\", 43);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, AutoScheduleComponent_div_22_ng_container_2_div_7_Template, 8, 0, \"div\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 45);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 46);\n    i0.ɵɵlistener(\"cdkDropListDropped\", function AutoScheduleComponent_div_22_ng_container_2_Template_div_cdkDropListDropped_10_listener($event) {\n      i0.ɵɵrestoreView(_r63);\n      const ctx_r62 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r62.drop($event));\n    });\n    i0.ɵɵtemplate(11, AutoScheduleComponent_div_22_ng_container_2_div_11_Template, 8, 14, \"div\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, AutoScheduleComponent_div_22_ng_container_2_footer_12_Template, 4, 0, \"footer\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const locationKey_r26 = ctx.$implicit;\n    const ctx_r25 = i0.ɵɵnextContext(2);\n    let tmp_2_0;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", locationKey_r26, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r25.isLock);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r25.getShortTime(((tmp_2_0 = ctx_r25.getConfig(ctx_r25.responseMetadata[\"locations\"][locationKey_r26].id)) == null ? null : tmp_2_0.begin_date) + \" \" + ((tmp_2_0 = ctx_r25.getConfig(ctx_r25.responseMetadata[\"locations\"][locationKey_r26].id)) == null ? null : tmp_2_0.begin_time) || \"\"), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"cdkDropListData\", ctx_r25.listMatches[locationKey_r26])(\"id\", locationKey_r26)(\"data_location_id\", ctx_r25.responseMetadata[\"locations\"][locationKey_r26].id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r25.listMatches[locationKey_r26]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r25.isLock);\n  }\n}\nfunction AutoScheduleComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 38);\n    i0.ɵɵtemplate(2, AutoScheduleComponent_div_22_ng_container_2_Template, 13, 8, \"ng-container\", 39);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.listLocationIds);\n  }\n}\nfunction AutoScheduleComponent_ng_container_23_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AutoScheduleComponent_ng_container_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AutoScheduleComponent_ng_container_23_ng_container_1_Template, 1, 0, \"ng-container\", 73);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r16 = i0.ɵɵreference(47);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r16);\n  }\n}\nfunction AutoScheduleComponent_ng_container_24_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AutoScheduleComponent_ng_container_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AutoScheduleComponent_ng_container_24_ng_container_1_Template, 1, 0, \"ng-container\", 73);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r18 = i0.ɵɵreference(49);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r18);\n  }\n}\nfunction AutoScheduleComponent_ng_container_34_div_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AutoScheduleComponent_ng_container_34_div_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AutoScheduleComponent_ng_container_34_div_1_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 60);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r67 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵnextContext(2);\n    const _r20 = i0.ɵɵreference(51);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r20)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c5, item_r67));\n  }\n}\nfunction AutoScheduleComponent_ng_container_34_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵtemplate(1, AutoScheduleComponent_ng_container_34_div_1_ng_container_1_Template, 2, 4, \"ng-container\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r67 = ctx.$implicit;\n    const ctx_r66 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"data_stage_id\", item_r67.stage_id)(\"data_time_slot_id\", item_r67.time_slot_id)(\"data_type\", item_r67.type)(\"cdkDragDisabled\", ctx_r66.isLock);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r67.type === \"match\");\n  }\n}\nfunction AutoScheduleComponent_ng_container_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AutoScheduleComponent_ng_container_34_div_1_Template, 2, 5, \"div\", 74);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.listUnScheduledMatches);\n  }\n}\nfunction AutoScheduleComponent_ng_container_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r72 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 76)(2, \"p\", 77);\n    i0.ɵɵtext(3, \" No matches found for this tournament. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_ng_container_35_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r72);\n      const ctx_r71 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r71.autoGenerate());\n    });\n    i0.ɵɵelement(5, \"i\", 79);\n    i0.ɵɵtext(6, \" Auto Generate \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AutoScheduleComponent_ng_template_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r75 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-modal-setup-schedule\", 80);\n    i0.ɵɵlistener(\"onSubmit\", function AutoScheduleComponent_ng_template_36_Template_app_modal_setup_schedule_onSubmit_0_listener($event) {\n      i0.ɵɵrestoreView(_r75);\n      const ctx_r74 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r74.onScheduleAction($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"seasonId\", ctx_r7.seasonId)(\"tournamentId\", ctx_r7.tournamentId)(\"tournamentInfo\", ctx_r7.tournamentInfo);\n  }\n}\nfunction AutoScheduleComponent_ng_template_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r78 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-modal-update-config\", 81);\n    i0.ɵɵlistener(\"onSubmit\", function AutoScheduleComponent_ng_template_38_Template_app_modal_update_config_onSubmit_0_listener($event) {\n      i0.ɵɵrestoreView(_r78);\n      const ctx_r77 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r77.onScheduleAction($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"selectedConfig\", ctx_r9.selectedConfig);\n  }\n}\nfunction AutoScheduleComponent_ng_template_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r81 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-modal-crud-break\", 82);\n    i0.ɵɵlistener(\"onSubmit\", function AutoScheduleComponent_ng_template_40_Template_app_modal_crud_break_onSubmit_0_listener($event) {\n      i0.ɵɵrestoreView(_r81);\n      const ctx_r80 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r80.onScheduleAction($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"breakModalParams\", ctx_r11.breakModalParams);\n  }\n}\nfunction AutoScheduleComponent_ng_template_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r84 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-modal-update-match\", 83);\n    i0.ɵɵlistener(\"onSubmit\", function AutoScheduleComponent_ng_template_42_Template_app_modal_update_match_onSubmit_0_listener($event) {\n      i0.ɵɵrestoreView(_r84);\n      const ctx_r83 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r83.onScheduleAction($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"timeSlotInfo\", ctx_r13.selectedItem)(\"seasonId\", ctx_r13.seasonId);\n  }\n}\nconst _c7 = function (a0) {\n  return {\n    \"text-danger\": a0\n  };\n};\nfunction AutoScheduleComponent_ng_template_44_div_9_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ref_r87 = i0.ɵɵnextContext().$implicit;\n    const ctx_r88 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c7, ctx_r88.isMatchHasConflict(ctx_r88.selectedMatchId, \"referee\", ref_r87.id)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", ref_r87.user == null ? null : ref_r87.user.first_name, \" \", ref_r87.user == null ? null : ref_r87.user.last_name, \" \");\n  }\n}\nfunction AutoScheduleComponent_ng_template_44_div_9_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ref_r87 = i0.ɵɵnextContext().$implicit;\n    const ctx_r89 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c7, ctx_r89.isMatchHasConflict(ctx_r89.selectedMatchId, \"referee\", ref_r87.id)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ref_r87.referee_name, \" \");\n  }\n}\nfunction AutoScheduleComponent_ng_template_44_div_9_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 96);\n  }\n}\nfunction AutoScheduleComponent_ng_template_44_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 91)(1, \"div\", 92);\n    i0.ɵɵtemplate(2, AutoScheduleComponent_ng_template_44_div_9_span_2_Template, 2, 5, \"span\", 93);\n    i0.ɵɵtemplate(3, AutoScheduleComponent_ng_template_44_div_9_span_3_Template, 2, 4, \"span\", 93);\n    i0.ɵɵelementStart(4, \"small\", 94);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, AutoScheduleComponent_ng_template_44_div_9_i_6_Template, 1, 0, \"i\", 95);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ref_r87 = ctx.$implicit;\n    const ctx_r86 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ref_r87.referee_type == \"user\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ref_r87.referee_type == \"freetext\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ref_r87.referee_type === \"user\" ? \"Registered User\" : \"External Referee\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r86.isMatchHasConflict(ctx_r86.selectedMatchId, \"referee\", ref_r87.id));\n  }\n}\nfunction AutoScheduleComponent_ng_template_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r94 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 84)(1, \"h4\", 85);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_ng_template_44_Template_button_click_4_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r94);\n      const modal_r85 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(modal_r85.dismiss());\n    });\n    i0.ɵɵelementStart(5, \"span\", 87);\n    i0.ɵɵtext(6, \"\\u00D7\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 88)(8, \"div\", 89);\n    i0.ɵɵtemplate(9, AutoScheduleComponent_ng_template_44_div_9_Template, 7, 4, \"div\", 90);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 2, \"Match Referees\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r15.selectedMatchReferees);\n  }\n}\nfunction AutoScheduleComponent_ng_template_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r96 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"div\", 98)(2, \"p\", 99);\n    i0.ɵɵtext(3, \"No Plan Created\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 100);\n    i0.ɵɵtext(5, \" Please enter the necessary information to allow the system to generate an accurate schedule based on your requirements. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_ng_template_46_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r96);\n      const ctx_r95 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r95.openModalSetupSchedule());\n    });\n    i0.ɵɵtext(7, \" Setup \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AutoScheduleComponent_ng_template_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 101)(1, \"div\", 98)(2, \"p\", 99);\n    i0.ɵɵtext(3, \"Fetching\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 100);\n    i0.ɵɵtext(5, \" Waiting for getting schedule data... \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AutoScheduleComponent_ng_template_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r99 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 102)(1, \"p\", 62);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 103)(4, \"div\", 104);\n    i0.ɵɵelement(5, \"img\", 105);\n    i0.ɵɵelementStart(6, \"span\", 106);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 107);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_ng_template_50_Template_button_click_8_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r99);\n      const item_r97 = restoredCtx.$implicit;\n      const ctx_r98 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r98.swapTeam(item_r97.match));\n    });\n    i0.ɵɵelement(9, \"i\", 108);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 109)(11, \"span\", 106);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"img\", 110);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r97 = ctx.$implicit;\n    let tmp_1_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r97.match.round_name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((tmp_1_0 = item_r97.match == null ? null : item_r97.match.home_team == null ? null : item_r97.match.home_team.name) !== null && tmp_1_0 !== undefined ? tmp_1_0 : \"TBD\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((tmp_2_0 = item_r97.match == null ? null : item_r97.match.away_team == null ? null : item_r97.match.away_team.name) !== null && tmp_2_0 !== undefined ? tmp_2_0 : \"TBD\");\n  }\n}\nfunction AutoScheduleComponent_ng_template_52_div_22_div_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ref_r104 = i0.ɵɵnextContext().$implicit;\n    const item_r100 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r106 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c7, ctx_r106.isMatchHasConflict(item_r100.id, \"referee\", ref_r104.id)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", ref_r104.user == null ? null : ref_r104.user.first_name, \" \", ref_r104.user == null ? null : ref_r104.user.last_name, \" \");\n  }\n}\nfunction AutoScheduleComponent_ng_template_52_div_22_div_2_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ref_r104 = i0.ɵɵnextContext().$implicit;\n    const item_r100 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r107 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c7, ctx_r107.isMatchHasConflict(item_r100.id, \"referee\", ref_r104.id)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ref_r104.referee_name, \" \");\n  }\n}\nfunction AutoScheduleComponent_ng_template_52_div_22_div_2_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"-\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AutoScheduleComponent_ng_template_52_div_22_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 118)(1, \"div\", 119);\n    i0.ɵɵtemplate(2, AutoScheduleComponent_ng_template_52_div_22_div_2_span_2_Template, 2, 5, \"span\", 93);\n    i0.ɵɵtemplate(3, AutoScheduleComponent_ng_template_52_div_22_div_2_span_3_Template, 2, 4, \"span\", 93);\n    i0.ɵɵtemplate(4, AutoScheduleComponent_ng_template_52_div_22_div_2_span_4_Template, 2, 0, \"span\", 19);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ref_r104 = ctx.$implicit;\n    const last_r105 = ctx.last;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ref_r104.referee_type == \"user\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ref_r104.referee_type == \"freetext\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !last_r105);\n  }\n}\nfunction AutoScheduleComponent_ng_template_52_div_22_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r115 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 120)(1, \"span\", 121);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_ng_template_52_div_22_div_3_Template_span_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r115);\n      const item_r100 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r113 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r113.toggleRefereeExpansion(item_r100.id, $event));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r100 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r103 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" +\", ctx_r103.getRemainingRefereesCount(item_r100.referees, item_r100.id), \" more \");\n  }\n}\nfunction AutoScheduleComponent_ng_template_52_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 114);\n    i0.ɵɵelement(1, \"i\", 115);\n    i0.ɵɵtemplate(2, AutoScheduleComponent_ng_template_52_div_22_div_2_Template, 5, 3, \"div\", 116);\n    i0.ɵɵtemplate(3, AutoScheduleComponent_ng_template_52_div_22_div_3_Template, 3, 1, \"div\", 117);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r100 = i0.ɵɵnextContext().$implicit;\n    const ctx_r101 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r101.getDisplayedReferees(item_r100.referees, item_r100.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r101.getRemainingRefereesCount(item_r100.referees, item_r100.id) > 0);\n  }\n}\nfunction AutoScheduleComponent_ng_template_52_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r119 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 102)(1, \"p\", 62);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 103)(4, \"div\", 104);\n    i0.ɵɵelement(5, \"img\", 105);\n    i0.ɵɵelementStart(6, \"span\", 111);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 107);\n    i0.ɵɵlistener(\"click\", function AutoScheduleComponent_ng_template_52_Template_button_click_8_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r119);\n      const item_r100 = restoredCtx.$implicit;\n      const ctx_r118 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r118.swapTeam(item_r100.match));\n    });\n    i0.ɵɵelement(9, \"i\", 108);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 109)(11, \"span\", 111);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"img\", 110);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 112);\n    i0.ɵɵelement(15, \"i\", 64);\n    i0.ɵɵelementStart(16, \"p\", 62);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\", 62);\n    i0.ɵɵtext(19, \"-\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\", 62);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(22, AutoScheduleComponent_ng_template_52_div_22_Template, 4, 2, \"div\", 113);\n  }\n  if (rf & 2) {\n    const item_r100 = ctx.$implicit;\n    const ctx_r23 = i0.ɵɵnextContext();\n    let tmp_2_0;\n    let tmp_4_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r100.match.round_name, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c7, ctx_r23.isMatchHasConflict(item_r100.id, \"team\", item_r100.match.home_team_id)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (tmp_2_0 = item_r100.match.home_team == null ? null : item_r100.match.home_team.name) !== null && tmp_2_0 !== undefined ? tmp_2_0 : \"TBD\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c7, ctx_r23.isMatchHasConflict(item_r100.id, \"team\", item_r100.match.away_team_id)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (tmp_4_0 = item_r100.match.away_team == null ? null : item_r100.match.away_team.name) !== null && tmp_4_0 !== undefined ? tmp_4_0 : \"TBD\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r23.getShortTime(item_r100.start_time), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r23.getShortTime(item_r100.end_time), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r100.referees && item_r100.referees.length > 0);\n  }\n}\nconst _c8 = function (a0, a1) {\n  return {\n    \"btn-outline-primary\": a0,\n    \"btn-primary\": a1\n  };\n};\nexport class AutoScheduleComponent {\n  constructor(_route, _router, _tournamentService, _stageService, _loadingService, _titleService, _translateService, _autoScheduleService, _modalService, _toastService) {\n    this._route = _route;\n    this._router = _router;\n    this._tournamentService = _tournamentService;\n    this._stageService = _stageService;\n    this._loadingService = _loadingService;\n    this._titleService = _titleService;\n    this._translateService = _translateService;\n    this._autoScheduleService = _autoScheduleService;\n    this._modalService = _modalService;\n    this._toastService = _toastService;\n    this.leagueOrGroupStageId = null;\n    this.dateOptions = [];\n    this.listLocations = {};\n    this.listMatches = {};\n    this.listLocationIds = [];\n    this.responseMetadata = {};\n    this.responseMatches = {};\n    this.teamConflicts = {};\n    this.refereeConflicts = {};\n    this.stageConflicts = {};\n    this.selectedConfig = null;\n    this.isFetching = true;\n    this.isLock = false;\n    this.hasMatches = false;\n    // public listUnScheduledMatches = [\n    // ];\n    this.listUnScheduledMatches = [];\n    this.selectedDate = null;\n    this.hasPlan = false;\n    // Simple dropdown state - track which dropdown is open\n    this.activeDropdownId = null;\n    // Referee expansion state\n    this.expandedRefereeMatches = new Set();\n    this.selectedMatchReferees = [];\n    this.selectedMatchId = null;\n    this.breakModalParams = {\n      locationId: null,\n      tournamentId: null,\n      timeSlotId: null,\n      lastTimeSlotId: null,\n      configId: null\n    };\n    this.selectedItem = null;\n    this.location = location;\n    this.isFetching = true;\n    this.tournamentId = this._route.snapshot.paramMap.get('tournament_id');\n    this._tournamentService.getTournament(this.tournamentId).subscribe(res => {\n      this.tournamentInfo = res;\n      this.leagueOrGroupStageId = res.stages.find(stage => {\n        if (this.tournamentInfo && this.tournamentInfo['type'] === AppConfig.TOURNAMENT_TYPES.groups_knockouts) {\n          return stage.type === AppConfig.TOURNAMENT_TYPES.groups;\n        } else {\n          return stage.type === AppConfig.TOURNAMENT_TYPES.league;\n        }\n      })?.id;\n      this.isLock = res.is_locked_schedule === 1;\n      this.seasonId = res.group.season.id;\n      _titleService.setTitle(res.name);\n      this.contentHeader = {\n        headerTitle: res.name,\n        actionButton: false,\n        breadcrumb: {\n          type: '',\n          links: [{\n            name: this._translateService.instant('Leagues'),\n            isLink: false\n          }, {\n            name: this._translateService.instant('Manage Leagues'),\n            isLink: true,\n            link: '/leagues/manage'\n          }, {\n            name: res.name,\n            isLink: false\n          }, {\n            name: this._translateService.instant('Auto Schedule'),\n            isLink: false\n          }]\n        }\n      };\n      this.onScheduleAction(null, true);\n    });\n  }\n  ngOnInit() {}\n  ngAfterViewChecked() {\n    setTimeout(() => {\n      this.hasMatches = this.responseMatches && Object.keys(this.responseMatches).length > 0 || this.listUnScheduledMatches.length > 0;\n    }, 0);\n  }\n  getScheduleMatches(showLoading = true) {\n    if (showLoading) {\n      this._loadingService.show();\n    }\n    this._autoScheduleService.getScheduleMatches(this.tournamentId).subscribe(res => {\n      this.responseMetadata = res.metadata;\n      this.responseMatches = Array.isArray(res.data) && res.data.length === 0 ? null : res.data;\n      this.teamConflicts = res.conflicts['team_scheduling_conflict'];\n      this.refereeConflicts = res.conflicts['referee_scheduling_conflict'];\n      this.stageConflicts = res.conflicts['stage_scheduling_conflict'];\n      if (this.responseMatches) {\n        this.dateOptions = Object.keys(this.responseMatches);\n        this.selectedDate = this.selectedDate && this.dateOptions.includes(this.selectedDate) ? this.selectedDate : this.dateOptions[0];\n        this.mapListLocations();\n      } else {\n        this.listLocationIds = [];\n        this.listMatches = {};\n        this.dateOptions = [];\n        this.selectedDate = null;\n      }\n      this._loadingService.dismiss();\n    }, () => {}, () => {\n      this.isFetching = false;\n      this.hasPlan = this.dateOptions.length > 0;\n    });\n  }\n  getUnScheduleMatches(showLoading = true) {\n    if (showLoading) {\n      this._loadingService.show();\n    }\n    this._autoScheduleService.getListUnScheduledMatches(this.tournamentId).subscribe(res => {\n      this.listUnScheduledMatches = res.data;\n      this._loadingService.dismiss();\n    });\n  }\n  mapListLocations() {\n    this.listLocationIds = [];\n    this.listMatches = {};\n    this.listLocations = this.responseMatches[this.selectedDate] || {};\n    if (!this.listLocations || !this.selectedDate) return;\n    Object.keys(this.listLocations).forEach(locationName => {\n      if (!this.listLocationIds.includes(`${locationName}`)) {\n        this.listLocationIds.push(`${locationName}`);\n      }\n      if (!this.listMatches[locationName]) {\n        this.listMatches[locationName] = [];\n      }\n      this.listMatches[locationName] = [...this.listMatches[locationName], ...this.listLocations[locationName]];\n    });\n  }\n  onSelectDate(event) {\n    this.selectedDate = event;\n    this.mapListLocations();\n  }\n  openModalSetupSchedule() {\n    this._modalService.open(this.modalSetupSchedule, {\n      centered: true,\n      size: 'lg'\n    });\n  }\n  onScheduleAction(response, showLoading = true) {\n    this.getScheduleMatches(showLoading);\n    this.getUnScheduleMatches(showLoading);\n  }\n  unScheduleMatch(timeSlotId, configId, onError, onSuccess) {\n    this._autoScheduleService.unScheduleMatch(timeSlotId, configId).subscribe(res => {\n      this.onScheduleAction(null, false);\n      onSuccess && onSuccess();\n    }, error => {\n      onError();\n    });\n  }\n  mapNewSlotIndex(locationKey) {\n    const newSlotIndex = {};\n    this.listMatches[locationKey].forEach((item, index) => {\n      newSlotIndex[item.time_slot_id] = index;\n    });\n    return newSlotIndex;\n  }\n  updateLocationMatch(locationKey, updateData, onError, onSuccess) {\n    const newIndex = this.mapNewSlotIndex(locationKey);\n    this._autoScheduleService.updateLocationMatch(updateData).subscribe(res => {\n      this.onScheduleAction(null, false);\n      onSuccess && onSuccess();\n    }, error => {\n      onError();\n    });\n  }\n  drop(event) {\n    const targetContainer = event.container.element.nativeElement;\n    const prevContainer = event.previousContainer.element.nativeElement;\n    const dragItem = event.item.element.nativeElement;\n    const targetContainerId = targetContainer['id'];\n    const prevContainerId = prevContainer['id'];\n    const targetStageId = targetContainer['data_stage_id'];\n    const targetLocationId = targetContainer['data_location_id'];\n    const prevStageId = prevContainer['data_stage_id'];\n    const prevLocationId = prevContainer['data_location_id'];\n    const itemStageId = dragItem['data_stage_id'];\n    const itemTimeSlotId = dragItem['data_time_slot_id'];\n    const itemType = dragItem['data_type'];\n    if (prevContainerId === targetContainerId && event.currentIndex === event.previousIndex) return;\n    if (targetContainerId === 'unScheduleZone') {\n      transferArrayItem(event.previousContainer.data, event.container.data, event.previousIndex, event.currentIndex);\n      if (targetContainerId === prevContainerId) return;\n      this.unScheduleMatch(itemTimeSlotId, this.getConfig(prevLocationId)?.id, () => {\n        transferArrayItem(event.container.data, event.previousContainer.data, event.currentIndex, event.previousIndex);\n        this._toastService.error(this._translateService.instant('Failed to unschedule match.'));\n      }, () => {\n        this._toastService.success(this._translateService.instant('Match unscheduled successfully.'));\n      });\n    } else {\n      if (event.previousContainer === event.container) {\n        moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);\n        this.updateLocationMatch(targetContainerId, {\n          new_index: this.mapNewSlotIndex(targetContainerId),\n          location_id: targetLocationId,\n          prev_location_id: prevLocationId,\n          stage_id: targetStageId,\n          prev_stage_id: prevStageId,\n          tournament_id: this.tournamentId,\n          config_id: this.getConfig(targetLocationId)?.id,\n          prev_config_id: this.getConfig(prevLocationId)?.id\n        }, () => {\n          moveItemInArray(event.container.data, event.currentIndex, event.previousIndex);\n          this._toastService.error(this._translateService.instant('Failed to update match schedule.'));\n        }, () => {\n          this._toastService.success(this._translateService.instant('Match schedule updated successfully.'));\n        });\n      } else {\n        transferArrayItem(event.previousContainer.data, event.container.data, event.previousIndex, event.currentIndex);\n        this.updateLocationMatch(targetContainerId, {\n          new_index: this.mapNewSlotIndex(targetContainerId),\n          location_id: targetLocationId,\n          prev_location_id: prevContainerId === 'unScheduleZone' ? null : prevLocationId,\n          stage_id: targetStageId,\n          prev_stage_id: prevContainerId === 'unScheduleZone' ? null : prevStageId,\n          tournament_id: this.tournamentId,\n          config_id: this.getConfig(targetLocationId)?.id,\n          prev_config_id: this.getConfig(prevLocationId)?.id\n        }, () => {\n          transferArrayItem(event.container.data, event.previousContainer.data, event.currentIndex, event.previousIndex);\n          this._toastService.error(this._translateService.instant('Failed to update match schedule.'));\n        }, () => {\n          this._toastService.success(this._translateService.instant('Match schedule updated successfully.'));\n        });\n      }\n    }\n  }\n  getShortTime(time) {\n    // handle return ISO string to short time format HH:mm and format 24 hours\n    if (!time) return '';\n    const date = new Date(time);\n    const hours = date.getHours().toString().padStart(2, '0');\n    const minutes = date.getMinutes().toString().padStart(2, '0');\n    return `${hours}:${minutes}`;\n  }\n  getConfig(location_id) {\n    return this.responseMetadata['configs'].find(item => {\n      return item.tournament_id === +this.tournamentId && item.location_id === +location_id && item.begin_date === this.selectedDate;\n    });\n  }\n  editSchedule(locationKey, configId) {\n    this.selectedConfig = {\n      tournamentId: this.tournamentId,\n      location: this.responseMetadata['locations'][locationKey].id,\n      date: this.selectedDate,\n      configId,\n      timeSlotIds: this.listMatches[locationKey].map(item => item.time_slot_id)\n    };\n    this._modalService.open(this.modalEditSchedule, {\n      centered: true\n    });\n  }\n  deletePlan(locationKey) {\n    const locationId = this.responseMetadata['locations'][locationKey].id;\n    const configId = this.getConfig(locationId)?.id;\n    const timeSlotIds = this.listMatches[locationKey].map(item => item.time_slot_id);\n    Swal.fire({\n      title: this._translateService.instant('Are you sure?'),\n      text: this._translateService.instant('You will not be able to recover this schedule!'),\n      icon: 'warning',\n      showCancelButton: true,\n      reverseButtons: true\n    }).then(result => {\n      if (result.isConfirmed) {\n        this._autoScheduleService.deleteSchedule(this.tournamentId, locationId, timeSlotIds, configId).subscribe(res => {\n          this.onScheduleAction(null, false);\n          this._toastService.success(this._translateService.instant('Schedule deleted successfully.'));\n        });\n      }\n    });\n  }\n  openModalAddBreak(locationId, lastTimeSlotId, configId) {\n    console.log(\"🚀 ~ AutoScheduleComponent ~ openModalAddBreak ~ configId:\", configId);\n    console.log(\"🚀 ~ AutoScheduleComponent ~ openModalAddBreak ~ lastTimeSlotId:\", lastTimeSlotId);\n    this.breakModalParams = {\n      ...this.breakModalParams,\n      locationId,\n      tournamentId: this.tournamentId,\n      lastTimeSlotId,\n      configId\n    };\n    this._modalService.open(this.modalCrudBreak, {\n      centered: true\n    });\n  }\n  // Simple dropdown methods\n  toggleDropdown(event, item) {\n    event.preventDefault();\n    event.stopPropagation();\n    if (event.target['className'] === 'swap-button') {\n      return;\n    }\n    const dropdownId = this.getDropdownId(item);\n    // Close dropdown if clicking on the same item\n    if (this.activeDropdownId === dropdownId) {\n      this.activeDropdownId = null;\n    } else {\n      // Open new dropdown (close any existing one)\n      this.activeDropdownId = dropdownId;\n    }\n  }\n  isDropdownOpen(item) {\n    const dropdownId = this.getDropdownId(item);\n    return this.activeDropdownId === dropdownId;\n  }\n  getDropdownId(item) {\n    // Create unique ID for each item\n    return `${item.time_slot_id}_${item.type}_${item.stage_id || 'unscheduled'}`;\n  }\n  closeAllDropdowns() {\n    this.activeDropdownId = null;\n  }\n  onDocumentClick(event) {\n    const target = event.target;\n    // Close dropdown if clicking outside of dropdown or dnd-item\n    if (!target.closest('.item-dropdown') && !target.closest('.dnd-item')) {\n      this.closeAllDropdowns();\n    }\n  }\n  // Dropdown action handlers\n  onEditMatch(item) {\n    this.closeAllDropdowns();\n    this.selectedItem = item;\n    this._modalService.open(this.modalUpdateMatch, {\n      centered: true\n    });\n  }\n  onUnscheduleTimeSlot(item, configId) {\n    const description = {\n      match: 'This match will be moved to unscheduled matches.',\n      break: 'This break will be removed.'\n    };\n    const successMessage = {\n      match: 'Match unscheduled successfully.',\n      break: 'Break removed successfully.'\n    };\n    const errorMessage = {\n      match: 'Failed to unschedule match.',\n      break: 'Failed to remove break.'\n    };\n    Swal.fire({\n      title: this._translateService.instant('Are you sure?'),\n      text: this._translateService.instant(description[item.type]),\n      icon: 'warning',\n      showCancelButton: true,\n      reverseButtons: true,\n      confirmButtonText: this._translateService.instant('Yes'),\n      cancelButtonText: this._translateService.instant('Cancel')\n    }).then(result => {\n      if (result.isConfirmed) {\n        this.unScheduleMatch(item.time_slot_id, configId, () => {\n          this._toastService.error(this._translateService.instant(errorMessage[item.type]));\n        }, () => {\n          this._toastService.success(this._translateService.instant(successMessage[item.type]));\n        });\n      }\n    });\n    this.closeAllDropdowns();\n  }\n  onEditEventTime(item, configId) {\n    this.closeAllDropdowns();\n    this.breakModalParams = {\n      timeSlotId: item.time_slot_id,\n      locationId: item.location_id,\n      tournamentId: item.tournament_id,\n      description: item.description,\n      breakDurations: item.break_durations,\n      configId: configId\n    };\n    this._modalService.open(this.modalCrudBreak, {\n      centered: true\n    });\n  }\n  clearSchedule() {\n    Swal.fire({\n      title: this._translateService.instant('Are you sure?'),\n      text: this._translateService.instant('This action will clear all schedule.'),\n      icon: 'warning',\n      showCancelButton: true,\n      reverseButtons: true,\n      confirmButtonText: this._translateService.instant('Yes, clear it!'),\n      cancelButtonText: this._translateService.instant('Cancel')\n    }).then(result => {\n      if (result.isConfirmed) {\n        this._autoScheduleService.clearSchedule(this.tournamentId).subscribe(res => {\n          Swal.fire({\n            title: this._translateService.instant('Success!'),\n            text: this._translateService.instant('Schedule has been cleared.'),\n            icon: 'success'\n          });\n          this.onScheduleAction(null, false);\n        }, error => {\n          Swal.fire({\n            title: this._translateService.instant('Error!'),\n            text: this._translateService.instant('Schedule has been cleared.'),\n            icon: 'success'\n          });\n        });\n      }\n    });\n  }\n  isMatchHasConflict(scheduleMatchId, type, itemCheckId) {\n    switch (type) {\n      case 'team':\n        return this.teamConflicts[scheduleMatchId]?.find(item => item.team_id === itemCheckId);\n      case 'referee':\n        return this.refereeConflicts[scheduleMatchId]?.find(item => item.referee_id === itemCheckId);\n      case 'match':\n        return this.teamConflicts[scheduleMatchId] || this.refereeConflicts[scheduleMatchId];\n    }\n  }\n  onClickLock() {\n    this.isLock = !this.isLock;\n    this._autoScheduleService.updateTournamentScheduleStatus(this.tournamentId).subscribe(res => {\n      this._toastService.success(res.message);\n    }, error => {\n      this.isLock = !this.isLock;\n      this._toastService.error(error.message);\n    });\n  }\n  swapTeam(matchInfo) {\n    this.closeAllDropdowns();\n    this._stageService.swapTeams(matchInfo).subscribe(res => {\n      this._toastService.success('Swap teams successfully.');\n      this.onScheduleAction(null, false);\n    }, error => {\n      this._toastService.error(error.message || 'Failed to swap teams.');\n    });\n  }\n  autoGenerate() {\n    this._stageService.autoGenerateMatches(this.leagueOrGroupStageId).subscribe(res => {\n      this._toastService.success('Auto generate matches successfully.');\n      this.onScheduleAction(null, false);\n    }, error => {\n      Swal.fire({\n        title: 'Cannot Auto Generate',\n        icon: error.warning ? 'warning' : 'error',\n        text: error.warning || error.message || 'Failed to auto generate matches.',\n        confirmButtonText: 'Go to Stage Details'\n      }).then(result => {\n        if (result.isConfirmed) {\n          this._router.navigate(['leagues', 'manage', this.tournamentId, 'stages', this.leagueOrGroupStageId]);\n        }\n      });\n    });\n  }\n  /**\r\n   * Get the display limit for referees based on their name lengths\r\n   * @param referees - Array of referees\r\n   * @param matchId - Match ID for expansion state\r\n   * @returns Maximum number of referees to show\r\n   */\n  getRefereeDisplayLimit(referees, matchId) {\n    if (this.expandedRefereeMatches.has(matchId)) {\n      return referees.length; // Show all if expanded\n    }\n    // Calculate average name length\n    const totalNameLength = referees.reduce((sum, ref) => {\n      const name = ref.referee_type === 'user' ? `${ref.user?.first_name || ''} ${ref.user?.last_name || ''}`.trim() : ref.referee_name || '';\n      return sum + name.length;\n    }, 0);\n    const averageLength = totalNameLength / referees.length;\n    // If average name length is long (>15 chars), show 3; otherwise show 4\n    return averageLength > 15 ? 3 : 4;\n  }\n  /**\r\n   * Get the list of referees to display (limited or all based on expansion state)\r\n   * @param referees - Array of all referees\r\n   * @param matchId - Match ID for expansion state\r\n   * @returns Array of referees to display\r\n   */\n  getDisplayedReferees(referees, matchId) {\n    if (!referees || referees.length === 0) {\n      return [];\n    }\n    const limit = this.getRefereeDisplayLimit(referees, matchId);\n    return referees.slice(0, limit);\n  }\n  /**\r\n   * Get the count of remaining referees not shown\r\n   * @param referees - Array of all referees\r\n   * @param matchId - Match ID for expansion state\r\n   * @returns Number of remaining referees\r\n   */\n  getRemainingRefereesCount(referees, matchId) {\n    if (!referees || referees.length === 0 || this.expandedRefereeMatches.has(matchId)) {\n      return 0;\n    }\n    const limit = this.getRefereeDisplayLimit(referees, matchId);\n    return Math.max(0, referees.length - limit);\n  }\n  /**\r\n   * Toggle referee expansion for a specific match\r\n   * @param matchId - Match ID\r\n   * @param event - Click event\r\n   */\n  toggleRefereeExpansion(matchId, event) {\n    event.stopPropagation();\n    event.preventDefault();\n    const matchItem = this.findMatchById(matchId);\n    if (!matchItem || !matchItem.referees || matchItem.referees.length === 0) {\n      return;\n    }\n    // If there are many referees (>6), show modal; otherwise toggle inline expansion\n    if (matchItem.referees.length > 6) {\n      this.showRefereeModal(matchItem.referees, matchId);\n    } else {\n      if (this.expandedRefereeMatches.has(matchId)) {\n        this.expandedRefereeMatches.delete(matchId);\n      } else {\n        this.expandedRefereeMatches.add(matchId);\n      }\n    }\n  }\n  /**\r\n   * Show modal with all referees for a match\r\n   * @param referees - Array of all referees\r\n   * @param matchId - Match ID\r\n   */\n  showRefereeModal(referees, matchId) {\n    this.selectedMatchReferees = referees;\n    this.selectedMatchId = matchId;\n    this._modalService.open(this.modalRefereeList, {\n      centered: true,\n      size: 'md'\n    });\n  }\n  /**\r\n   * Find a match by its ID across all locations\r\n   * @param matchId - Match ID to search for\r\n   * @returns Match item or null if not found\r\n   */\n  findMatchById(matchId) {\n    for (const locationKey of this.listLocationIds) {\n      const matches = this.listMatches[locationKey] || [];\n      const match = matches.find(item => item.id === matchId);\n      if (match) {\n        return match;\n      }\n    }\n    // Also check unscheduled matches\n    return this.listUnScheduledMatches.find(item => item.id === matchId) || null;\n  }\n  static #_ = this.ɵfac = function AutoScheduleComponent_Factory(t) {\n    return new (t || AutoScheduleComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.TournamentService), i0.ɵɵdirectiveInject(i3.StageService), i0.ɵɵdirectiveInject(i4.LoadingService), i0.ɵɵdirectiveInject(i5.Title), i0.ɵɵdirectiveInject(i6.TranslateService), i0.ɵɵdirectiveInject(i7.AutoScheduleService), i0.ɵɵdirectiveInject(i8.NgbModal), i0.ɵɵdirectiveInject(i9.ToastrService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AutoScheduleComponent,\n    selectors: [[\"app-auto-schedule\"]],\n    viewQuery: function AutoScheduleComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(_c2, 5);\n        i0.ɵɵviewQuery(_c3, 5);\n        i0.ɵɵviewQuery(_c4, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalSetupSchedule = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalEditSchedule = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalCrudBreak = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalUpdateMatch = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.modalRefereeList = _t.first);\n      }\n    },\n    hostBindings: function AutoScheduleComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function AutoScheduleComponent_click_HostBindingHandler($event) {\n          return ctx.onDocumentClick($event);\n        }, false, i0.ɵɵresolveDocument);\n      }\n    },\n    decls: 54,\n    vars: 24,\n    consts: [[1, \"content-wrapper\", \"container-xxl\", \"p-0\"], [1, \"content-body\"], [3, \"contentHeader\"], [1, \"top-header\"], [1, \"row\", \"mb-1\"], [1, \"d-flex\", \"flex-column\", 2, \"gap\", \"2px\"], [\"for\", \"selectDate\"], [\"id\", \"selectDate\", 2, \"min-width\", \"200px\", 3, \"searchable\", \"clearable\", \"placeholder\", \"ngModel\", \"ngModelChange\", \"change\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"align-items-center\", 2, \"gap\", \"8px\"], [1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [\"data-feather\", \"settings\", 1, \"mr-25\"], [1, \"btn\", \"btn-icon\", 3, \"ngClass\", \"click\"], [3, \"ngClass\"], [1, \"btn\", \"btn-icon\", \"btn-outline-danger\", 3, \"disabled\", \"click\"], [\"data-feather\", \"trash-2\"], [\"cdkDropListGroup\", \"\"], [\"id\", \"listLocationZone\", 1, \"col-9\"], [\"class\", \"horizontal-scroll-container\", 4, \"ngIf\"], [4, \"ngIf\"], [\"id\", \"listUnScheduleZone\", 1, \"col-3\"], [1, \"unschedule-container\"], [1, \"location-column\", \"unplanned-matches-container\"], [1, \"location-header\"], [1, \"h4\"], [1, \"small\"], [\"cdkDropList\", \"\", \"id\", \"unScheduleZone\", 1, \"dnd-zone\", 3, \"cdkDropListData\", \"cdkDropListDropped\"], [\"modalSetupSchedule\", \"\"], [\"modalEditSchedule\", \"\"], [\"modalCrudBreak\", \"\"], [\"modalUpdateMatch\", \"\"], [\"modalRefereeList\", \"\"], [\"noSchedule\", \"\"], [\"fetchingState\", \"\"], [\"matchNotScheduledTemplate\", \"\"], [\"matchScheduledTemplate\", \"\"], [3, \"value\"], [1, \"horizontal-scroll-container\"], [1, \"location-columns-wrapper\"], [4, \"ngFor\", \"ngForOf\"], [1, \"location-column\", \"mb-2\"], [1, \"bg-white\", \"shadow-sm\"], [1, \"d-flex\", \"align-items-start\", \"justify-content-between\"], [1, \"location-name\", \"h4\"], [\"class\", \"\", \"ngbDropdown\", \"\", 4, \"ngIf\"], [1, \"location-stage-name\"], [\"cdkDropList\", \"\", 1, \"dnd-zone\", 3, \"cdkDropListData\", \"id\", \"data_location_id\", \"cdkDropListDropped\"], [\"class\", \"dnd-item location-match-row\", \"cdkDrag\", \"\", 3, \"ngClass\", \"data_stage_id\", \"data_time_slot_id\", \"data_type\", \"cdkDragDisabled\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"location-footer\", 4, \"ngIf\"], [\"ngbDropdown\", \"\", 1, \"\"], [\"ngbDropdownToggle\", \"\", 1, \"btn\", \"btn-link\", \"dropdown-toggle\"], [1, \"fa-solid\", \"fa-ellipsis-vertical\"], [\"ngbDropdownMenu\", \"\", \"aria-labelledby\", \"dropdownMenuButton\"], [\"ngbDropdownItem\", \"\", 3, \"click\"], [\"cdkDrag\", \"\", 1, \"dnd-item\", \"location-match-row\", 3, \"ngClass\", \"data_stage_id\", \"data_time_slot_id\", \"data_type\", \"cdkDragDisabled\", \"click\"], [\"type\", \"button\", \"rippleEffect\", \"\", \"class\", \"conflict-tooltip btn btn-link\", \"placement\", \"right\", \"container\", \"body\", \"ngbTooltip\", \"This match has conflict\", 4, \"ngIf\"], [1, \"item-dropdown\"], [1, \"dropdown-content\"], [\"type\", \"button\", \"rippleEffect\", \"\", \"placement\", \"right\", \"container\", \"body\", \"ngbTooltip\", \"This match has conflict\", 1, \"conflict-tooltip\", \"btn\", \"btn-link\"], [1, \"fa-light\", \"fa-circle-exclamation\", 2, \"font-size\", \"16px\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"break-info-header\"], [1, \"text-center\", \"m-0\"], [1, \"break-row\"], [\"aria-hidden\", \"true\", 1, \"fa-regular\", \"fa-clock\"], [1, \"break-time\", \"m-0\"], [1, \"dropdown-item\", 3, \"click\"], [1, \"fa-regular\", \"fa-whistle\", \"mr-2\", 2, \"rotate\", \"-45deg\"], [1, \"fa\", \"fa-trash\", \"mr-2\"], [1, \"fa\", \"fa-clock\", \"mr-2\"], [1, \"location-footer\"], [1, \"btn\", \"btn-link\", \"w-100\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-plus\"], [4, \"ngTemplateOutlet\"], [\"class\", \"dnd-item location-match-row\", \"cdkDrag\", \"\", 3, \"data_stage_id\", \"data_time_slot_id\", \"data_type\", \"cdkDragDisabled\", 4, \"ngFor\", \"ngForOf\"], [\"cdkDrag\", \"\", 1, \"dnd-item\", \"location-match-row\", 3, \"data_stage_id\", \"data_time_slot_id\", \"data_type\", \"cdkDragDisabled\"], [\"id\", \"notHaveMatches\"], [1, \"text-center\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fa-solid\", \"fa-wand-magic-sparkles\"], [3, \"seasonId\", \"tournamentId\", \"tournamentInfo\", \"onSubmit\"], [3, \"selectedConfig\", \"onSubmit\"], [3, \"breakModalParams\", \"onSubmit\"], [3, \"timeSlotInfo\", \"seasonId\", \"onSubmit\"], [1, \"modal-header\"], [\"id\", \"modal-title\", 1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"btn-close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [1, \"modal-body\"], [1, \"referee-list\"], [\"class\", \"referee-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"referee-item\"], [1, \"referee-info\"], [3, \"ngClass\", 4, \"ngIf\"], [1, \"referee-type\"], [\"class\", \"fa-solid fa-exclamation-triangle text-danger\", \"title\", \"This referee has a conflict\", 4, \"ngIf\"], [\"title\", \"This referee has a conflict\", 1, \"fa-solid\", \"fa-exclamation-triangle\", \"text-danger\"], [\"id\", \"noSchedule\"], [1, \"col\", \"d-flex\", \"flex-column\", \"align-items-center\", \"justify-content-center\", \"g-2\", 2, \"height\", \"500px\"], [1, \"h5\"], [1, \"w-75\", \"text-center\", 2, \"color\", \"rgba(168, 170, 174, 1)\"], [\"id\", \"fetchingState\"], [1, \"match-info-header\"], [1, \"team-row\"], [1, \"home-team\"], [\"src\", \"https://assets.codepen.io/285131/whufc.svg\", \"alt\", \"Home Team Logo\", 1, \"team-logo\"], [1, \"h6\", \"team-name\"], [1, \"swap-button\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"fa\", \"fa-right-left\"], [1, \"away-team\"], [\"src\", \"https://assets.codepen.io/285131/whufc.svg\", \"alt\", \"Away Team Logo\", 1, \"team-logo\"], [1, \"h6\", \"team-name\", 3, \"ngClass\"], [1, \"match-date\"], [\"class\", \"referees-row\", 4, \"ngIf\"], [1, \"referees-row\"], [1, \"fa-regular\", \"fa-whistle\", 2, \"rotate\", \"-45deg\"], [\"class\", \"referee-names\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"referee-more\", 4, \"ngIf\"], [1, \"referee-names\"], [1, \"referee-name\"], [1, \"referee-more\"], [1, \"referee-expand-btn\", 3, \"click\"]],\n    template: function AutoScheduleComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-content-header\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"label\", 6);\n        i0.ɵɵtext(7);\n        i0.ɵɵpipe(8, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"ng-select\", 7);\n        i0.ɵɵlistener(\"ngModelChange\", function AutoScheduleComponent_Template_ng_select_ngModelChange_9_listener($event) {\n          return ctx.selectedDate = $event;\n        })(\"change\", function AutoScheduleComponent_Template_ng_select_change_9_listener($event) {\n          return ctx.onSelectDate($event);\n        });\n        i0.ɵɵpipe(10, \"translate\");\n        i0.ɵɵtemplate(11, AutoScheduleComponent_ng_option_11_Template, 2, 2, \"ng-option\", 8);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(12, \"div\", 9)(13, \"button\", 10);\n        i0.ɵɵlistener(\"click\", function AutoScheduleComponent_Template_button_click_13_listener() {\n          return ctx.openModalSetupSchedule();\n        });\n        i0.ɵɵelement(14, \"i\", 11);\n        i0.ɵɵtext(15, \" Add Schedule \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"button\", 12);\n        i0.ɵɵlistener(\"click\", function AutoScheduleComponent_Template_button_click_16_listener() {\n          return ctx.onClickLock();\n        });\n        i0.ɵɵelement(17, \"i\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(18, \"button\", 14);\n        i0.ɵɵlistener(\"click\", function AutoScheduleComponent_Template_button_click_18_listener() {\n          return ctx.clearSchedule();\n        });\n        i0.ɵɵelement(19, \"i\", 15);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(20, \"div\", 16)(21, \"div\", 17);\n        i0.ɵɵtemplate(22, AutoScheduleComponent_div_22_Template, 3, 1, \"div\", 18);\n        i0.ɵɵtemplate(23, AutoScheduleComponent_ng_container_23_Template, 2, 1, \"ng-container\", 19);\n        i0.ɵɵtemplate(24, AutoScheduleComponent_ng_container_24_Template, 2, 1, \"ng-container\", 19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"div\", 20)(26, \"div\", 21)(27, \"div\", 22)(28, \"div\", 23)(29, \"p\", 24);\n        i0.ɵɵtext(30, \"Not Planned\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(31, \"p\", 25);\n        i0.ɵɵtext(32, \" You can add unscheduled matches to the calendar by drag and drop them. \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(33, \"div\", 26);\n        i0.ɵɵlistener(\"cdkDropListDropped\", function AutoScheduleComponent_Template_div_cdkDropListDropped_33_listener($event) {\n          return ctx.drop($event);\n        });\n        i0.ɵɵtemplate(34, AutoScheduleComponent_ng_container_34_Template, 2, 1, \"ng-container\", 19);\n        i0.ɵɵtemplate(35, AutoScheduleComponent_ng_container_35_Template, 7, 0, \"ng-container\", 19);\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵtemplate(36, AutoScheduleComponent_ng_template_36_Template, 1, 3, \"ng-template\", null, 27, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(38, AutoScheduleComponent_ng_template_38_Template, 1, 1, \"ng-template\", null, 28, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(40, AutoScheduleComponent_ng_template_40_Template, 1, 1, \"ng-template\", null, 29, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(42, AutoScheduleComponent_ng_template_42_Template, 1, 2, \"ng-template\", null, 30, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(44, AutoScheduleComponent_ng_template_44_Template, 10, 4, \"ng-template\", null, 31, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(46, AutoScheduleComponent_ng_template_46_Template, 8, 0, \"ng-template\", null, 32, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(48, AutoScheduleComponent_ng_template_48_Template, 6, 0, \"ng-template\", null, 33, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(50, AutoScheduleComponent_ng_template_50_Template, 14, 3, \"ng-template\", null, 34, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(52, AutoScheduleComponent_ng_template_52_Template, 23, 12, \"ng-template\", null, 35, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"contentHeader\", ctx.contentHeader);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(8, 17, \"Date\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(10, 19, \"Select Date\"));\n        i0.ɵɵproperty(\"searchable\", true)(\"clearable\", false)(\"ngModel\", ctx.selectedDate);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.dateOptions);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", ctx.isLock);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(21, _c8, !ctx.isLock, ctx.isLock));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngClass\", ctx.isLock ? \"fa-regular fa-lock\" : \"fa-regular fa-unlock\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"disabled\", ctx.isLock);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasPlan);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.hasPlan && !ctx.isFetching && !ctx.isLock);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isFetching);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"cdkDropListData\", ctx.listUnScheduledMatches);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasMatches);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.hasMatches);\n      }\n    },\n    dependencies: [i10.NgClass, i10.NgForOf, i10.NgIf, i10.NgTemplateOutlet, i11.DefaultClassDirective, i12.NgControlStatus, i12.NgModel, i13.RippleEffectDirective, i14.FeatherIconDirective, i8.NgbDropdown, i8.NgbDropdownToggle, i8.NgbDropdownMenu, i8.NgbDropdownItem, i8.NgbTooltip, i15.ContentHeaderComponent, i16.NgSelectComponent, i16.ɵr, i17.CdkDropList, i17.CdkDropListGroup, i17.CdkDrag, i18.ModalSetupScheduleComponent, i19.ModalUpdateConfigComponent, i20.ModalCrudBreakComponent, i21.ModalUpdateMatchComponent, i6.TranslatePipe],\n    styles: [\".home-team[_ngcontent-%COMP%], .away-team[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n}\\n\\nimg.team-logo[_ngcontent-%COMP%] {\\n  width: 2.5rem;\\n  height: 2.5rem;\\n}\\n\\n.swap-button[_ngcontent-%COMP%] {\\n  color: rgb(45, 103, 241);\\n  background: rgba(45, 103, 241, 0.2);\\n  border-width: 0;\\n  padding: 0.25rem 1rem;\\n  border-radius: 0.5rem;\\n}\\n\\n.top-header[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 0;\\n  z-index: 15;\\n  background-color: white;\\n  padding: 1rem;\\n  margin: 0;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  width: 100%;\\n  min-width: 100%;\\n}\\n.top-header[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin: 0;\\n  width: 100%;\\n  min-width: 100%;\\n}\\n\\n.dnd-zone[_ngcontent-%COMP%] {\\n  background: #fff;\\n  min-height: 10rem;\\n}\\n\\n.dnd-item[_ngcontent-%COMP%] {\\n  -webkit-user-select: none;\\n          user-select: none;\\n  border-top: 1px solid rgba(168, 170, 174, 0.25);\\n  padding: 1rem 0.25rem;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  gap: 4px;\\n  height: 10rem;\\n  cursor: grab;\\n  position: relative;\\n  background: #fff;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .conflict-tooltip[_ngcontent-%COMP%] {\\n  padding: 0;\\n  position: absolute;\\n  top: 4px;\\n  right: 4px;\\n  color: red;\\n}\\n.dnd-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(45, 103, 241, 0.05);\\n}\\n.dnd-item[_ngcontent-%COMP%]:active {\\n  cursor: grabbing;\\n}\\n.dnd-item.cdk-drag-preview[_ngcontent-%COMP%] {\\n  z-index: 999;\\n  border-radius: 8px;\\n  border: 1px solid #a8aaae;\\n  scale: 0.95;\\n}\\n.dnd-item.cdk-drag-preview[_ngcontent-%COMP%]   .item-dropdown[_ngcontent-%COMP%] {\\n  display: none !important;\\n}\\n.dnd-item.cdk-drag-preview.conflict-border[_ngcontent-%COMP%] {\\n  border: 1px solid rgba(255, 0, 0, 0.5) !important;\\n}\\n.dnd-item.location-match-row[_ngcontent-%COMP%] {\\n  padding: 1.5rem 2rem;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .match-info-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: rgb(75, 70, 92);\\n}\\n.dnd-item[_ngcontent-%COMP%]   .item-dropdown[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 100%;\\n  margin-left: -2rem;\\n  z-index: 1000;\\n  background: white;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  border: 1px solid rgba(168, 170, 174, 0.25);\\n  min-width: 180px;\\n  overflow: hidden;\\n  display: none;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .item-dropdown.visible[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .item-dropdown[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n  padding: 0.75rem 1rem;\\n  border: none;\\n  background: none;\\n  text-align: left;\\n  color: rgb(75, 70, 92);\\n  font-size: 1rem;\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n  white-space: nowrap;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .item-dropdown[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(45, 103, 241, 0.1);\\n  color: rgb(45, 103, 241);\\n}\\n.dnd-item[_ngcontent-%COMP%]   .item-dropdown[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  background-color: rgba(45, 103, 241, 0.1);\\n  color: rgb(45, 103, 241);\\n}\\n.dnd-item[_ngcontent-%COMP%]   .item-dropdown[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  width: 16px;\\n  text-align: center;\\n  margin-right: 0.5rem;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .team-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .team-name[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .match-date[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  justify-content: center;\\n  align-items: center;\\n  color: rgb(75, 70, 92);\\n}\\n.dnd-item[_ngcontent-%COMP%]   .referees-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 0.5rem;\\n  color: rgb(75, 70, 92);\\n  flex-wrap: wrap;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .referee-names[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .referee-name[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.25rem;\\n  align-items: center;\\n  white-space: nowrap;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .referee-more[_ngcontent-%COMP%] {\\n  margin-left: 0.5rem;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .referee-expand-btn[_ngcontent-%COMP%] {\\n  color: rgb(45, 103, 241);\\n  cursor: pointer;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  text-decoration: underline;\\n  transition: color 0.2s ease;\\n}\\n.dnd-item[_ngcontent-%COMP%]   .referee-expand-btn[_ngcontent-%COMP%]:hover {\\n  color: rgba(45, 103, 241, 0.8);\\n}\\n.dnd-item[_ngcontent-%COMP%]   .break-info-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: rgb(16, 15, 15);\\n}\\n.dnd-item[_ngcontent-%COMP%]   .break-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 0.5rem;\\n  color: rgb(75, 70, 92);\\n}\\n\\n.content-wrapper[_ngcontent-%COMP%] {\\n  overflow: visible !important;\\n  position: relative;\\n}\\n\\n.content-body[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: visible;\\n  width: 100%;\\n}\\n\\n#listLocationZone[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  overflow-x: auto;\\n  padding: 0 !important;\\n  margin: 1rem 0;\\n}\\n#listLocationZone[_ngcontent-%COMP%]   .horizontal-scroll-container[_ngcontent-%COMP%] {\\n  padding-bottom: 1rem;\\n  scroll-behavior: smooth;\\n  width: 100%;\\n}\\n#listLocationZone[_ngcontent-%COMP%]   .horizontal-scroll-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 8px;\\n}\\n#listLocationZone[_ngcontent-%COMP%]   .horizontal-scroll-container[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 4px;\\n}\\n#listLocationZone[_ngcontent-%COMP%]   .horizontal-scroll-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(45, 103, 241, 0.5);\\n  border-radius: 4px;\\n}\\n#listLocationZone[_ngcontent-%COMP%]   .horizontal-scroll-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(45, 103, 241, 0.7);\\n}\\n#listLocationZone[_ngcontent-%COMP%]   .horizontal-scroll-container[_ngcontent-%COMP%]   .location-columns-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  min-width: max-content;\\n  width: max-content;\\n  position: relative;\\n}\\n#listLocationZone[_ngcontent-%COMP%]   .horizontal-scroll-container[_ngcontent-%COMP%]   .location-columns-wrapper[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  display: block;\\n  min-width: 28rem;\\n  flex-shrink: 0;\\n  height: 1px;\\n}\\n\\n#listUnScheduleZone[_ngcontent-%COMP%] {\\n  width: 100%;\\n  position: absolute;\\n  top: calc(10rem - 2px);\\n  right: 0;\\n  height: 100%;\\n  z-index: 10;\\n  margin: 1rem 0;\\n}\\n#listUnScheduleZone[_ngcontent-%COMP%]   .unschedule-container[_ngcontent-%COMP%] {\\n  position: sticky !important;\\n  top: 8rem;\\n  height: max-content;\\n  z-index: 10;\\n  width: 100%;\\n  right: 0;\\n  margin: 1rem 0;\\n  background: white;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n@media (max-width: 1200px) {\\n  #listUnScheduleZone[_ngcontent-%COMP%]   .unschedule-container[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-width: 100%;\\n    right: 0;\\n    margin: 1rem 0;\\n  }\\n}\\n#listUnScheduleZone[_ngcontent-%COMP%]   .unschedule-container[_ngcontent-%COMP%]   #unScheduleZone[_ngcontent-%COMP%] {\\n  z-index: 100;\\n  max-height: 50vh;\\n  overflow-y: auto;\\n}\\n#listUnScheduleZone[_ngcontent-%COMP%]   .unschedule-container[_ngcontent-%COMP%]   .unplanned-matches-container[_ngcontent-%COMP%] {\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\\n  height: max-content;\\n  max-height: calc(100vh - 15rem);\\n}\\n#listUnScheduleZone[_ngcontent-%COMP%]   .unschedule-container[_ngcontent-%COMP%]   .unplanned-matches-container.location-column[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n#listUnScheduleZone[_ngcontent-%COMP%]   .unschedule-container[_ngcontent-%COMP%]   .unplanned-matches-container[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%] {\\n  background-color: white !important;\\n  border-bottom: 1px solid rgba(168, 170, 174, 0.25);\\n}\\n\\n.location-column[_ngcontent-%COMP%] {\\n  width: 26rem;\\n  height: max-content;\\n  overflow: visible;\\n  border: 1px solid #eee;\\n}\\n.location-column.conflict[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%] {\\n  background: rgba(255, 0, 0, 0.1);\\n}\\n.location-column.conflict[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%]   .location-name[_ngcontent-%COMP%] {\\n  color: rgb(255, 0, 0);\\n}\\n.location-column.conflict[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%]   .location-stage-name[_ngcontent-%COMP%] {\\n  color: rgb(255, 0, 0);\\n}\\n.location-column.conflict[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%]   .btn.btn-link[_ngcontent-%COMP%] {\\n  color: rgb(255, 0, 0);\\n}\\n.location-column[_ngcontent-%COMP%]   .stage-conflict-tooltip[_ngcontent-%COMP%] {\\n  padding: 0;\\n  position: absolute;\\n  bottom: 1rem;\\n  right: 0;\\n  color: red;\\n}\\n.location-column[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n  padding: 1rem 0.5rem;\\n  background: rgba(45, 103, 241, 0.25);\\n  justify-content: space-between;\\n  height: 8rem;\\n  position: relative;\\n}\\n.location-column[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%]   .btn.btn-link[_ngcontent-%COMP%] {\\n  padding: 0 1rem;\\n}\\n.location-column[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%]   .dropdown-toggle[_ngcontent-%COMP%] {\\n  border: none;\\n}\\n.location-column[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%]   .dropdown-toggle[_ngcontent-%COMP%]::after {\\n  width: 0;\\n  background-image: none;\\n}\\n.location-column[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%]   .location-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  margin: 0;\\n  width: 90%;\\n  color: rgb(45, 103, 241);\\n}\\n.location-column[_ngcontent-%COMP%]   .location-header[_ngcontent-%COMP%]   .location-stage-name[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: rgb(45, 103, 241);\\n}\\n.location-column[_ngcontent-%COMP%]   .location-footer[_ngcontent-%COMP%] {\\n  border-top: 1px solid rgba(168, 170, 174, 0.25);\\n  padding: 1rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .top-header[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .top-header[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n    align-items: stretch;\\n    padding-left: 0.5rem;\\n    padding-right: 0.5rem;\\n  }\\n  #listUnScheduleZone[_ngcontent-%COMP%] {\\n    position: relative !important;\\n    left: auto;\\n    right: auto;\\n    top: auto;\\n    width: 100%;\\n    max-width: 100%;\\n    margin-top: 2rem;\\n  }\\n  .location-column[_ngcontent-%COMP%] {\\n    min-width: 250px;\\n  }\\n  .dnd-item[_ngcontent-%COMP%]   .referees-row[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n    gap: 0.25rem;\\n  }\\n  .dnd-item[_ngcontent-%COMP%]   .referees-row[_ngcontent-%COMP%]   .referee-names[_ngcontent-%COMP%] {\\n    gap: 0.15rem;\\n  }\\n  .dnd-item[_ngcontent-%COMP%]   .referees-row[_ngcontent-%COMP%]   .referee-name[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  .dnd-item[_ngcontent-%COMP%]   .referees-row[_ngcontent-%COMP%]   .referee-expand-btn[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .location-column[_ngcontent-%COMP%] {\\n    min-width: 200px;\\n  }\\n  .top-header[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .top-header[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%] {\\n    padding-left: 0.25rem;\\n    padding-right: 0.25rem;\\n  }\\n  .dnd-item[_ngcontent-%COMP%]   .referees-row[_ngcontent-%COMP%]   .referee-name[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .dnd-item[_ngcontent-%COMP%]   .referees-row[_ngcontent-%COMP%]   .referee-expand-btn[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n  .referee-list[_ngcontent-%COMP%]   .referee-item[_ngcontent-%COMP%] {\\n    padding: 0.5rem 0;\\n  }\\n  .referee-list[_ngcontent-%COMP%]   .referee-item[_ngcontent-%COMP%]   .referee-info[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .referee-list[_ngcontent-%COMP%]   .referee-item[_ngcontent-%COMP%]   .referee-info[_ngcontent-%COMP%]   .referee-type[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .dnd-item[_ngcontent-%COMP%]   .item-dropdown[_ngcontent-%COMP%] {\\n    left: 0;\\n    top: 100%;\\n    margin-left: 0;\\n    margin-top: 5px;\\n    min-width: 200px;\\n    max-width: 90vw;\\n  }\\n}\\n.horizontal-scroll-container[_ngcontent-%COMP%] {\\n  overflow: visible;\\n}\\n\\n.conflict-border[_ngcontent-%COMP%] {\\n  border: 1px solid rgba(255, 0, 0, 0.5) !important;\\n}\\n\\n.cdk-drag-placeholder[_ngcontent-%COMP%] {\\n  opacity: 0;\\n}\\n\\n.cdk-drag-disabled[_ngcontent-%COMP%] {\\n  background: rgba(220, 220, 220, 0.25);\\n  cursor: default;\\n}\\n.cdk-drag-disabled[_ngcontent-%COMP%]:hover {\\n  background: rgba(220, 220, 220, 0.25);\\n}\\n\\n#fetchingState[_ngcontent-%COMP%], #noSchedule[_ngcontent-%COMP%] {\\n  width: 75%;\\n}\\n\\n#notHaveMatches[_ngcontent-%COMP%] {\\n  width: 100%;\\n  min-height: 10rem;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.dropdown-menu[_ngcontent-%COMP%] {\\n  z-index: 1000;\\n}\\n\\n.referee-list[_ngcontent-%COMP%]   .referee-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0.75rem 0;\\n  border-bottom: 1px solid rgba(168, 170, 174, 0.25);\\n}\\n.referee-list[_ngcontent-%COMP%]   .referee-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.referee-list[_ngcontent-%COMP%]   .referee-item[_ngcontent-%COMP%]   .referee-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n.referee-list[_ngcontent-%COMP%]   .referee-item[_ngcontent-%COMP%]   .referee-info[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 0.95rem;\\n}\\n.referee-list[_ngcontent-%COMP%]   .referee-item[_ngcontent-%COMP%]   .referee-info[_ngcontent-%COMP%]   .referee-type[_ngcontent-%COMP%] {\\n  color: rgba(75, 70, 92, 0.7);\\n  font-size: 0.8rem;\\n  font-style: italic;\\n}\\n.referee-list[_ngcontent-%COMP%]   .referee-item[_ngcontent-%COMP%]   i.fa-exclamation-triangle[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n}\\n\\n.modal-header[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  font-size: 1.5rem;\\n  cursor: pointer;\\n  color: rgb(75, 70, 92);\\n}\\n.modal-header[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%]:hover {\\n  color: rgb(45, 103, 241);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAOA,SAAsBA,eAAe,EAAEC,iBAAiB,QAAQ,wBAAwB;AAExF,OAAOC,IAAI,MAAM,aAAa;AAG9B,SAASC,SAAS,QAAQ,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICAnCC,qCAA2D;IAAAA,YAC3D;IAAAA,iBAAY;;;;IADgCA,gCAAc;IAACA,eAC3D;IAD2DA,wCAC3D;;;;;;IAkCUA,+BAA0C;IAEtCA,wBAA6C;IAC/CA,iBAAS;IACTA,+BAA0D;IACrCA;MAAAA;MAAA;MAAA;MAAA;MAAA,OAE7BA,gEAECC,2CAA8D,WACtE,sBACY,8BACG;IAAA,EAAK;IACDD,2BACF;IAAAA,iBAAI;IACJA,6BAAqD;IAAlCA;MAAAA;MAAA;MAAA;MAAA,OAASA,kDAAuB;IAAA,EAAC;IAClDA,6BACF;IAAAA,iBAAI;;;;;IA2BRA,kCAE+C;IAC7CA,wBAAsE;IACxEA,iBAAS;;;;;IAGPA,wBAGmB;;;;;;;;;;IAJrBA,6BAA4C;IAC1CA,qIAGmB;IACrBA,0BAAe;;;;;;IAJEA,eAEiB;IAFjBA,uCAEiB;;;;;IAGlCA,6BAA4C;IAC1CA,+BAA+B;IAE3BA,YACF;IAAAA,iBAAI;IAENA,+BAAuB;IACrBA,wBAAsD;IACtDA,6BAA0B;IACxBA,YACF;IAAAA,iBAAI;IAERA,0BAAe;;;;IATTA,eACF;IADEA,qEACF;IAKEA,eACF;IADEA,8DACF;;;;;;IAQAA,6BAA4C;IAC1CA,kCAA0D;IAA5BA;MAAAA;MAAA;MAAA;MAAA,OAASA,4CAAiB;IAAA,EAAC;IACvDA,wBAAiE;IACjEA,YACF;;IAAAA,iBAAS;IACTA,kCAQI;IAR0BA;MAAAA;MAAA;MAAA;MAAA;MAAA;MAAA,OAEhDA,iEAESE,2CAChB,WAAW,sBAEQ,8BACG;IAAA,EAAM;IACDF,wBAAgC;IAChCA,YACF;;IAAAA,iBAAS;IACXA,0BAAe;;;IAdXA,eACF;IADEA,8EACF;IAWEA,eACF;IADEA,yEACF;;;;;;IAIFA,6BAA4C;IAC1CA,kCAQI;IAR0BA;MAAAA;MAAA;MAAA;MAAA;MAAA;MAAA,OAE3CA,4DAEIG,2CAChB,WAAW,sBAEQ,8BACG;IAAA,EAAM;IACDH,wBAAgC;IAChCA,YACF;;IAAAA,iBAAS;IACTA,kCAQI;IAR0BA;MAAAA;MAAA;MAAA;MAAA;MAAA;MAAA,OAEhDA,iEAESI,2CAChB,WAAW,sBAEQ,8BACG;IAAA,EAAM;IACDJ,wBAAgC;IAChCA,YACF;;IAAAA,iBAAS;IACXA,0BAAe;;;IAdXA,eACF;IADEA,wEACF;IAWEA,eACF;IADEA,qEACF;;;;;;;;;;;IA/ERA,+BAIoD;IAAlDA;MAAA;MAAA;MAAA;MAAA,OAASA,kCAAWK,wCAA4B;IAAA,EAAC;IACjDL,0GAIS;IAETA,sHAKe;IACfA,sHAYe;IAGfA,+BAAkE;IAG9DA,sHAiBe;IAGfA,sHAyBe;IACjBA,iBAAM;;;;;IAjF6EA,uGAEnF;IAKCA,eAA0C;IAA1CA,uEAA0C;IAI9BA,eAA2B;IAA3BA,gDAA2B;IAM3BA,eAA2B;IAA3BA,gDAA2B;IAefA,eAAsC;IAAtCA,2DAAsC;IAG9CA,eAA2B;IAA3BA,gDAA2B;IAoB3BA,eAA2B;IAA3BA,gDAA2B;;;;;;IA8BlDA,kCAAgD;IACXA;MAAAA;MAAA;MAAA;MAAA;MAAA,OAElDA,kEAA8D,WAC/D,0GAEe,CAAC,sGAAD,CAAC,2BAEZM,2CAAwD,WAChE,sBACY,8BACG;IAAA,EAAQ;IACDN,wBAA6C;IAC7CA,mCACF;IAAAA,iBAAS;;;;;;IAlJjBA,6BAA0D;IACxDA,+BAAkC;IAKxBA,YACF;IAAAA,iBAAI;IACJA,6FAmBM;IACRA,iBAAM;IACNA,6BAA+B;IAC7BA,YAWF;IAAAA,iBAAI;IAENA,gCAGI;IAFFA;MAAAA;MAAA;MAAA,OAAsBA,mCAAY;IAAA,EAAC;IAGnCA,gGAmFM;IACRA,iBAAM;IACNA,qGAeS;IACXA,iBAAM;IAEVA,0BAAe;;;;;;IAhJHA,eACF;IADEA,gDACF;IAC2BA,eAAa;IAAbA,sCAAa;IAsBxCA,eAWF;IAXEA,kUAWF;IAEgCA,eAA4C;IAA5CA,sEAA4C;IAItDA,eAA2B;IAA3BA,8DAA2B;IAqFlBA,eAAa;IAAbA,sCAAa;;;;;IAtIxDA,+BAAyD;IAErDA,iGAsJe;IACjBA,iBAAM;;;;IAvJkCA,eAAkB;IAAlBA,gDAAkB;;;;;IA0J1DA,wBAA4D;;;;;IAD9DA,6BAAyD;IACvDA,yGAA4D;IAC9DA,0BAAe;;;;;IADEA,eAA4B;IAA5BA,uCAA4B;;;;;IAG3CA,wBAA+D;;;;;IADjEA,6BAAiC;IAC/BA,yGAA+D;IACjEA,0BAAe;;;;;IADEA,eAA+B;IAA/BA,uCAA+B;;;;;IAoBpCA,wBAGmB;;;;;IAJrBA,6BAA4C;IAC1CA,8HAGmB;IACrBA,0BAAe;;;;;;IAJEA,eAEa;IAFbA,uCAEa;;;;;IANhCA,+BAE6B;IAC3BA,+GAKe;IACjBA,iBAAM;;;;;IARJA,iDAA+B;IAEhBA,eAA2B;IAA3BA,gDAA2B;;;;;IAJ9CA,6BAAiC;IAC/BA,uFASM;IACRA,0BAAe;;;;IAVSA,eAAyB;IAAzBA,uDAAyB;;;;;;IAWjDA,6BAAkC;IAChCA,+BAAyB;IAErBA,uDACF;IAAAA,iBAAI;IAEJA,kCAAyD;IAAjDA;MAAAA;MAAA;MAAA,OAASA,qCAAc;IAAA,EAAC;IAC9BA,wBAA+C;IAC/CA,+BACF;IAAAA,iBAAS;IAEbA,0BAAe;;;;;;IAS3BA,oDAC0C;IAAxCA;MAAAA;MAAA;MAAA,OAAYA,+CAAwB;IAAA,EAAC;IADvCA,iBAC0C;;;;IADhBA,0CAAqB;;;;;;IAI/CA,mDAAmG;IAAxCA;MAAAA;MAAA;MAAA,OAAYA,+CAAwB;IAAA,EAAC;IAAhGA,iBAAmG;;;;IAA1EA,sDAAiC;;;;;;IAG1DA,gDAAoG;IAAxCA;MAAAA;MAAA;MAAA,OAAYA,+CAAwB;IAAA,EAAC;IAAjGA,iBAAoG;;;;IAA9EA,2DAAqC;;;;;;IAI3DA,kDAAoH;IAAxCA;MAAAA;MAAA;MAAA,OAAYA,+CAAwB;IAAA,EAAC;IAAjHA,iBAAoH;;;;IAA5FA,mDAA6B;;;;;;;;;;IAc7CA,gCAEK;IACHA,YACF;IAAAA,iBAAO;;;;;IAJkCA,gIAErC;IACFA,eACF;IADEA,iJACF;;;;;IACAA,gCAEK;IACHA,YACF;IAAAA,iBAAO;;;;;IAJsCA,gIAEzC;IACFA,eACF;IADEA,qDACF;;;;;IAGFA,wBAC+F;;;;;IAfjGA,+BAAoE;IAEhEA,8FAIO;IACPA,8FAIO;IACPA,iCAA4B;IAAAA,YAA0E;IAAAA,iBAAQ;IAEhHA,wFAC+F;IACjGA,iBAAM;;;;;IAdKA,eAAgC;IAAhCA,qDAAgC;IAKhCA,eAAoC;IAApCA,yDAAoC;IAKfA,eAA0E;IAA1EA,8FAA0E;IAEpGA,eAA4D;IAA5DA,iGAA4D;;;;;;IAtBtEA,+BAA0B;IACiBA,YAAkC;;IAAAA,iBAAK;IAChFA,kCAAqF;IAA1BA;MAAA;MAAA;MAAA,OAASA,kCAAe;IAAA,EAAC;IAClFA,gCAAyB;IAAAA,sBAAO;IAAAA,iBAAO;IAG3CA,+BAAwB;IAEpBA,sFAgBM;IACRA,iBAAM;;;;IAxBmCA,eAAkC;IAAlCA,4DAAkC;IAO/BA,eAAwB;IAAxBA,uDAAwB;;;;;;IAsBtEA,+BAAqB;IAEHA,+BAAe;IAAAA,iBAAI;IACjCA,8BAAkE;IAChEA,yIAEF;IAAAA,iBAAI;IACJA,kCAAmE;IAAnCA;MAAAA;MAAA;MAAA,OAASA,+CAAwB;IAAA,EAAC;IAChEA,uBACF;IAAAA,iBAAS;;;;;IAMbA,gCAAwB;IAENA,wBAAQ;IAAAA,iBAAI;IAC1BA,8BAAkE;IAChEA,sDACF;IAAAA,iBAAI;;;;;;IAMRA,gCAA+B;IACFA,YAA2B;IAAAA,iBAAI;IAE5DA,gCAAsB;IAElBA,2BAA+F;IAC/FA,iCAA2B;IAAAA,YAEvB;IAAAA,iBAAO;IAEbA,mCAA2D;IAA/BA;MAAA;MAAA;MAAA;MAAA,OAASA,+CAAoB;IAAA,EAAC;IACxDA,yBAAmD;IACrDA,iBAAS;IACTA,iCAAuB;IACMA,aAEvB;IAAAA,iBAAO;IACXA,4BAA+F;IACjGA,iBAAM;;;;;;IAjBqBA,eAA2B;IAA3BA,+CAA2B;IAKzBA,eAEvB;IAFuBA,6LAEvB;IAMuBA,eAEvB;IAFuBA,6LAEvB;;;;;IAyDFA,gCAEK;IACHA,YACF;IAAAA,iBAAO;;;;;;IAJkCA,uHAErC;IACFA,eACF;IADEA,qJACF;;;;;IACAA,gCAEK;IACHA,YACF;IAAAA,iBAAO;;;;;;IAJsCA,uHAEzC;IACFA,eACF;IADEA,sDACF;;;;;IACAA,4BAAoB;IAAAA,iBAAC;IAAAA,iBAAO;;;;;IAZhCA,gCAA6G;IAEzGA,qGAIO;IACPA,qGAIO;IACPA,qGAA4B;IAC9BA,iBAAM;;;;;IAXGA,eAAgC;IAAhCA,sDAAgC;IAKhCA,eAAoC;IAApCA,0DAAoC;IAKpCA,eAAW;IAAXA,iCAAW;;;;;;IAKtBA,gCAAwF;IACrDA;MAAAA;MAAA;MAAA;MAAA,OAASA,oEAAuC;IAAA,EAAC;IAChFA,YACF;IAAAA,iBAAO;;;;;IADLA,eACF;IADEA,2GACF;;;;;IAxBJA,gCAA4E;IAC1EA,yBAA4D;IAG5DA,8FAcM;IAGNA,8FAIM;IACRA,iBAAM;;;;;IAtBuCA,eAAiD;IAAjDA,yFAAiD;IAiBjEA,eAA2D;IAA3DA,+FAA2D;;;;;;IAjExFA,gCAA+B;IAE3BA,YACF;IAAAA,iBAAI;IAENA,gCAAsB;IAElBA,2BAA+F;IAC/FA,iCAMK;IACHA,YACF;IAAAA,iBAAO;IAETA,mCAA2D;IAA/BA;MAAA;MAAA;MAAA;MAAA,OAASA,iDAAoB;IAAA,EAAC;IACxDA,yBAAmD;IACrDA,iBAAS;IACTA,iCAAuB;IAQnBA,aACF;IAAAA,iBAAO;IACPA,4BAA+F;IACjGA,iBAAM;IAERA,iCAAwB;IACtBA,yBAAsD;IACtDA,8BAA2B;IACzBA,aACF;IAAAA,iBAAI;IACJA,8BAA2B;IAAAA,kBAAC;IAAAA,iBAAI;IAChCA,8BAA2B;IACzBA,aACF;IAAAA,iBAAI;IAENA,yFA0BM;;;;;;;IApEFA,eACF;IADEA,2DACF;IAK6BA,eAMvB;IANuBA,oIAMvB;IACFA,eACF;IADEA,0KACF;IAM2BA,eAMvB;IANuBA,qIAMvB;IACFA,eACF;IADEA,0KACF;IAOAA,eACF;IADEA,2EACF;IAGEA,eACF;IADEA,yEACF;IAEyBA,eAA+C;IAA/CA,0EAA+C;;;;;;;;;ADtW5E,OAAM,MAAOO,qBAAqB;EA0D9BC,YACWC,MAAsB,EACtBC,OAAe,EACfC,kBAAqC,EACrCC,aAA2B,EAC3BC,eAA+B,EAC/BC,aAAoB,EACpBC,iBAAmC,EACnCC,oBAAyC,EACxCC,aAAuB,EACvBC,aAA4B;IAT7B,WAAM,GAANT,MAAM;IACN,YAAO,GAAPC,OAAO;IACP,uBAAkB,GAAlBC,kBAAkB;IAClB,kBAAa,GAAbC,aAAa;IACb,oBAAe,GAAfC,eAAe;IACf,kBAAa,GAAbC,aAAa;IACb,sBAAiB,GAAjBC,iBAAiB;IACjB,yBAAoB,GAApBC,oBAAoB;IACnB,kBAAa,GAAbC,aAAa;IACb,kBAAa,GAAbC,aAAa;IA/DlB,yBAAoB,GAAkB,IAAI;IAG1C,gBAAW,GAAG,EAAE;IAChB,kBAAa,GAAG,EAAE;IAClB,gBAAW,GAAG,EAAE;IAChB,oBAAe,GAAG,EAAE;IACpB,qBAAgB,GAAG,EAAE;IACrB,oBAAe,GAAc,EAAE;IAE/B,kBAAa,GAAG,EAAE;IAClB,qBAAgB,GAAG,EAAE;IACrB,mBAAc,GAAG,EAAE;IAGnB,mBAAc,GAA8B,IAAI;IAEhD,eAAU,GAAG,IAAI;IACjB,WAAM,GAAG,KAAK;IACd,eAAU,GAAG,KAAK;IAgBzB;IAEA;IAEO,2BAAsB,GAAG,EAAE;IAE3B,iBAAY,GAAG,IAAI;IAEnB,YAAO,GAAG,KAAK;IAEtB;IACO,qBAAgB,GAAkB,IAAI;IAE7C;IACO,2BAAsB,GAAgB,IAAIC,GAAG,EAAE;IAC/C,0BAAqB,GAAU,EAAE;IACjC,oBAAe,GAAkB,IAAI;IAiVrC,qBAAgB,GAAqB;MACxCC,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChBC,cAAc,EAAE,IAAI;MACpBC,QAAQ,EAAE;KACb;IAkEM,iBAAY,GAAG,IAAI;IAsQP,aAAQ,GAAGC,QAAQ;IAjpBlC,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACL,YAAY,GAAG,IAAI,CAACZ,MAAM,CAACkB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,eAAe,CAAC;IACtE,IAAI,CAAClB,kBAAkB,CAACmB,aAAa,CAAC,IAAI,CAACT,YAAY,CAAC,CAACU,SAAS,CAC7DC,GAAG,IAAI;MACJ,IAAI,CAACC,cAAc,GAAGD,GAAG;MAEzB,IAAI,CAACE,oBAAoB,GAAGF,GAAG,CAACG,MAAM,CAACC,IAAI,CAAEC,KAAK,IAAI;QAClD,IAAI,IAAI,CAACJ,cAAc,IAAI,IAAI,CAACA,cAAc,CAAC,MAAM,CAAC,KAAKlC,SAAS,CAACuC,gBAAgB,CAACC,gBAAgB,EAAE;UACpG,OAAOF,KAAK,CAACG,IAAI,KAAKzC,SAAS,CAACuC,gBAAgB,CAACG,MAAM;SAC1D,MAAM;UACH,OAAOJ,KAAK,CAACG,IAAI,KAAKzC,SAAS,CAACuC,gBAAgB,CAACI,MAAM;;MAE/D,CAAC,CAAC,EAAEC,EAAE;MAEN,IAAI,CAACC,MAAM,GAAGZ,GAAG,CAACa,kBAAkB,KAAK,CAAC;MAC1C,IAAI,CAACC,QAAQ,GAAGd,GAAG,CAACe,KAAK,CAACC,MAAM,CAACL,EAAE;MACnC7B,aAAa,CAACmC,QAAQ,CAACjB,GAAG,CAACkB,IAAI,CAAC;MAEhC,IAAI,CAACC,aAAa,GAAG;QACjBC,WAAW,EAAEpB,GAAG,CAACkB,IAAI;QACrBG,YAAY,EAAE,KAAK;QACnBC,UAAU,EAAE;UACRd,IAAI,EAAE,EAAE;UACRe,KAAK,EAAE,CACH;YACIL,IAAI,EAAE,IAAI,CAACnC,iBAAiB,CAACyC,OAAO,CAAC,SAAS,CAAC;YAC/CC,MAAM,EAAE;WACX,EACD;YACIP,IAAI,EAAE,IAAI,CAACnC,iBAAiB,CAACyC,OAAO,CAAC,gBAAgB,CAAC;YACtDC,MAAM,EAAE,IAAI;YACZC,IAAI,EAAE;WACT,EACD;YACIR,IAAI,EAAElB,GAAG,CAACkB,IAAI;YACdO,MAAM,EAAE;WACX,EACD;YACIP,IAAI,EAAE,IAAI,CAACnC,iBAAiB,CAACyC,OAAO,CAAC,eAAe,CAAC;YACrDC,MAAM,EAAE;WACX;;OAGZ;MAED,IAAI,CAACE,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC;IACrC,CAAC,CAAC;EACV;EAEAC,QAAQ,IACR;EAEAC,kBAAkB;IACdC,UAAU,CAAC,MAAK;MACZ,IAAI,CAACC,UAAU,GAAI,IAAI,CAACC,eAAe,IAAIC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACF,eAAe,CAAC,CAACG,MAAM,GAAG,CAAC,IAAK,IAAI,CAACC,sBAAsB,CAACD,MAAM,GAAG,CAAC;IACtI,CAAC,EAAE,CAAC,CAAC;EACT;EAEAE,kBAAkB,CAACC,cAAuB,IAAI;IAC1C,IAAIA,WAAW,EAAE;MACb,IAAI,CAACzD,eAAe,CAAC0D,IAAI,EAAE;;IAI/B,IAAI,CAACvD,oBAAoB,CAACqD,kBAAkB,CAAC,IAAI,CAAChD,YAAY,CAAC,CAC1DU,SAAS,CAAEC,GAAG,IAAI;MACf,IAAI,CAACwC,gBAAgB,GAAGxC,GAAG,CAACyC,QAAQ;MACpC,IAAI,CAACT,eAAe,GAAGU,KAAK,CAACC,OAAO,CAAC3C,GAAG,CAAC4C,IAAI,CAAC,IAAI5C,GAAG,CAAC4C,IAAI,CAACT,MAAM,KAAK,CAAC,GAAG,IAAI,GAAGnC,GAAG,CAAC4C,IAAI;MAEzF,IAAI,CAACC,aAAa,GAAG7C,GAAG,CAAC8C,SAAS,CAAC,0BAA0B,CAAC;MAC9D,IAAI,CAACC,gBAAgB,GAAG/C,GAAG,CAAC8C,SAAS,CAAC,6BAA6B,CAAC;MACpE,IAAI,CAACE,cAAc,GAAGhD,GAAG,CAAC8C,SAAS,CAAC,2BAA2B,CAAC;MAEhE,IAAI,IAAI,CAACd,eAAe,EAAE;QACtB,IAAI,CAACiB,WAAW,GAAGhB,MAAM,CAACC,IAAI,CAAC,IAAI,CAACF,eAAe,CAAC;QACpD,IAAI,CAACkB,YAAY,GAAG,IAAI,CAACA,YAAY,IAAI,IAAI,CAACD,WAAW,CAACE,QAAQ,CAAC,IAAI,CAACD,YAAY,CAAC,GAC/E,IAAI,CAACA,YAAY,GACjB,IAAI,CAACD,WAAW,CAAC,CAAC,CAAC;QACzB,IAAI,CAACG,gBAAgB,EAAE;OAC1B,MAAM;QACH,IAAI,CAACC,eAAe,GAAG,EAAE;QACzB,IAAI,CAACC,WAAW,GAAG,EAAE;QACrB,IAAI,CAACL,WAAW,GAAG,EAAE;QACrB,IAAI,CAACC,YAAY,GAAG,IAAI;;MAG5B,IAAI,CAACrE,eAAe,CAAC0E,OAAO,EAAE;IAClC,CAAC,EAAE,MAAK,CACR,CAAC,EAAE,MAAK;MACJ,IAAI,CAAC7D,UAAU,GAAG,KAAK;MACvB,IAAI,CAAC8D,OAAO,GAAG,IAAI,CAACP,WAAW,CAACd,MAAM,GAAG,CAAC;IAC9C,CAAC,CAAC;EACV;EAEAsB,oBAAoB,CAACnB,WAAW,GAAG,IAAI;IACnC,IAAIA,WAAW,EAAE;MACb,IAAI,CAACzD,eAAe,CAAC0D,IAAI,EAAE;;IAE/B,IAAI,CAACvD,oBAAoB,CAAC0E,yBAAyB,CAAC,IAAI,CAACrE,YAAY,CAAC,CACjEU,SAAS,CAAEC,GAAG,IAAI;MACf,IAAI,CAACoC,sBAAsB,GAAGpC,GAAG,CAAC4C,IAAI;MAEtC,IAAI,CAAC/D,eAAe,CAAC0E,OAAO,EAAE;IAClC,CAAC,CAAC;EACV;EAEAH,gBAAgB;IACZ,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,WAAW,GAAG,EAAE;IAErB,IAAI,CAACK,aAAa,GAAG,IAAI,CAAC3B,eAAe,CAAC,IAAI,CAACkB,YAAY,CAAC,IAAI,EAAE;IAElE,IAAI,CAAC,IAAI,CAACS,aAAa,IAAI,CAAC,IAAI,CAACT,YAAY,EAAE;IAE/CjB,MAAM,CAACC,IAAI,CAAC,IAAI,CAACyB,aAAa,CAAC,CAACC,OAAO,CAAEC,YAAoB,IAAI;MAE7D,IAAI,CAAC,IAAI,CAACR,eAAe,CAACF,QAAQ,CAAC,GAAGU,YAAY,EAAE,CAAC,EAAE;QACnD,IAAI,CAACR,eAAe,CAACS,IAAI,CAAC,GAAGD,YAAY,EAAE,CAAC;;MAGhD,IAAI,CAAC,IAAI,CAACP,WAAW,CAACO,YAAY,CAAC,EAAE;QACjC,IAAI,CAACP,WAAW,CAACO,YAAY,CAAC,GAAG,EAAE;;MAGvC,IAAI,CAACP,WAAW,CAACO,YAAY,CAAC,GAAG,CAC7B,GAAG,IAAI,CAACP,WAAW,CAACO,YAAY,CAAC,EACjC,GAAG,IAAI,CAACF,aAAa,CAACE,YAAY,CAAC,CACtC;IACL,CAAC,CAAC;EAEN;EAEAE,YAAY,CAACC,KAAK;IACd,IAAI,CAACd,YAAY,GAAGc,KAAK;IACzB,IAAI,CAACZ,gBAAgB,EAAE;EAC3B;EAEAa,sBAAsB;IAClB,IAAI,CAAChF,aAAa,CAACiF,IAAI,CAAC,IAAI,CAACC,kBAAkB,EAAE;MAC7CC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE;KACT,CAAC;EACN;EAEA1C,gBAAgB,CAAC2C,QAAQ,EAAEhC,cAAuB,IAAI;IAClD,IAAI,CAACD,kBAAkB,CAACC,WAAW,CAAC;IACpC,IAAI,CAACmB,oBAAoB,CAACnB,WAAW,CAAC;EAC1C;EAEAiC,eAAe,CAACjF,UAA2B,EAAEE,QAAyB,EAAEgF,OAAmB,EAAEC,SAAqB;IAC9G,IAAI,CAACzF,oBAAoB,CAACuF,eAAe,CAACjF,UAAU,EAAEE,QAAQ,CAAC,CAACO,SAAS,CAAEC,GAAG,IAAI;MAC9E,IAAI,CAAC2B,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC;MAClC8C,SAAS,IAAIA,SAAS,EAAE;IAC5B,CAAC,EAAGC,KAAK,IAAI;MACTF,OAAO,EAAE;IACb,CAAC,CAAC;EACN;EAGAG,eAAe,CAACC,WAAW;IAEvB,MAAMC,YAAY,GAAG,EAAE;IACvB,IAAI,CAACvB,WAAW,CAACsB,WAAW,CAAC,CAAChB,OAAO,CAAC,CAACkB,IAAI,EAAEC,KAAK,KAAI;MAElDF,YAAY,CAACC,IAAI,CAACE,YAAY,CAAC,GAAGD,KAAK;IAC3C,CAAC,CAAC;IACF,OAAOF,YAAY;EACvB;EAEAI,mBAAmB,CAACL,WAAmB,EAAEM,UAAqC,EAAEV,OAAmB,EAAEC,SAAqB;IACtH,MAAMU,QAAQ,GAAG,IAAI,CAACR,eAAe,CAACC,WAAW,CAAC;IAClD,IAAI,CAAC5F,oBAAoB,CAACiG,mBAAmB,CAACC,UAAU,CAAC,CAACnF,SAAS,CAAEC,GAAG,IAAI;MACxE,IAAI,CAAC2B,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC;MAClC8C,SAAS,IAAIA,SAAS,EAAE;IAC5B,CAAC,EAAGC,KAAK,IAAI;MACTF,OAAO,EAAE;IACb,CAAC,CAAC;EACN;EAGAY,IAAI,CAACpB,KAA4B;IAC7B,MAAMqB,eAAe,GAAGrB,KAAK,CAACsB,SAAS,CAACC,OAAO,CAACC,aAAa;IAC7D,MAAMC,aAAa,GAAGzB,KAAK,CAAC0B,iBAAiB,CAACH,OAAO,CAACC,aAAa;IACnE,MAAMG,QAAQ,GAAG3B,KAAK,CAACc,IAAI,CAACS,OAAO,CAACC,aAAa;IAEjD,MAAMI,iBAAiB,GAAGP,eAAe,CAAC,IAAI,CAAC;IAC/C,MAAMQ,eAAe,GAAGJ,aAAa,CAAC,IAAI,CAAC;IAE3C,MAAMK,aAAa,GAAGT,eAAe,CAAC,eAAe,CAAC;IACtD,MAAMU,gBAAgB,GAAGV,eAAe,CAAC,kBAAkB,CAAC;IAE5D,MAAMW,WAAW,GAAGP,aAAa,CAAC,eAAe,CAAC;IAClD,MAAMQ,cAAc,GAAGR,aAAa,CAAC,kBAAkB,CAAC;IAExD,MAAMS,WAAW,GAAGP,QAAQ,CAAC,eAAe,CAAC;IAC7C,MAAMQ,cAAc,GAAGR,QAAQ,CAAC,mBAAmB,CAAC;IACpD,MAAMS,QAAQ,GAAGT,QAAQ,CAAC,WAAW,CAAC;IAEtC,IAAIE,eAAe,KAAKD,iBAAiB,IAAI5B,KAAK,CAACqC,YAAY,KAAKrC,KAAK,CAACsC,aAAa,EAAE;IAEzF,IAAIV,iBAAiB,KAAK,gBAAgB,EAAE;MACxC/H,iBAAiB,CACbmG,KAAK,CAAC0B,iBAAiB,CAAC9C,IAAI,EAC5BoB,KAAK,CAACsB,SAAS,CAAC1C,IAAI,EACpBoB,KAAK,CAACsC,aAAa,EACnBtC,KAAK,CAACqC,YAAY,CACrB;MAED,IAAIT,iBAAiB,KAAKC,eAAe,EAAE;MAE3C,IAAI,CAACtB,eAAe,CAAC4B,cAAc,EAAE,IAAI,CAACI,SAAS,CAACN,cAAc,CAAC,EAAEtF,EAAE,EAAE,MAAK;QAC1E9C,iBAAiB,CACbmG,KAAK,CAACsB,SAAS,CAAC1C,IAAI,EACpBoB,KAAK,CAAC0B,iBAAiB,CAAC9C,IAAI,EAC5BoB,KAAK,CAACqC,YAAY,EAClBrC,KAAK,CAACsC,aAAa,CACtB;QACD,IAAI,CAACpH,aAAa,CAACwF,KAAK,CAAC,IAAI,CAAC3F,iBAAiB,CAACyC,OAAO,CAAC,6BAA6B,CAAC,CAAC;MAC3F,CAAC,EAAE,MAAK;QACJ,IAAI,CAACtC,aAAa,CAACsH,OAAO,CAAC,IAAI,CAACzH,iBAAiB,CAACyC,OAAO,CAAC,iCAAiC,CAAC,CAAC;MACjG,CAAC,CAAC;KACL,MAAM;MACH,IAAIwC,KAAK,CAAC0B,iBAAiB,KAAK1B,KAAK,CAACsB,SAAS,EAAE;QAC7C1H,eAAe,CAACoG,KAAK,CAACsB,SAAS,CAAC1C,IAAI,EAAEoB,KAAK,CAACsC,aAAa,EAAEtC,KAAK,CAACqC,YAAY,CAAC;QAC9E,IAAI,CAACpB,mBAAmB,CAACW,iBAAiB,EAAE;UACxCa,SAAS,EAAE,IAAI,CAAC9B,eAAe,CAACiB,iBAAiB,CAAC;UAClDc,WAAW,EAAEX,gBAAgB;UAC7BY,gBAAgB,EAAEV,cAAc;UAChCW,QAAQ,EAAEd,aAAa;UACvBe,aAAa,EAAEb,WAAW;UAC1Bc,aAAa,EAAE,IAAI,CAACzH,YAAY;UAChC0H,SAAS,EAAE,IAAI,CAACR,SAAS,CAACR,gBAAgB,CAAC,EAAEpF,EAAE;UAC/CqG,cAAc,EAAE,IAAI,CAACT,SAAS,CAACN,cAAc,CAAC,EAAEtF;SACnD,EAAE,MAAK;UACJ/C,eAAe,CAACoG,KAAK,CAACsB,SAAS,CAAC1C,IAAI,EAAEoB,KAAK,CAACqC,YAAY,EAAErC,KAAK,CAACsC,aAAa,CAAC;UAC9E,IAAI,CAACpH,aAAa,CAACwF,KAAK,CAAC,IAAI,CAAC3F,iBAAiB,CAACyC,OAAO,CAAC,kCAAkC,CAAC,CAAC;QAChG,CAAC,EAAE,MAAK;UACJ,IAAI,CAACtC,aAAa,CAACsH,OAAO,CAAC,IAAI,CAACzH,iBAAiB,CAACyC,OAAO,CAAC,sCAAsC,CAAC,CAAC;QACtG,CAAC,CAAC;OACL,MAAM;QACH3D,iBAAiB,CACbmG,KAAK,CAAC0B,iBAAiB,CAAC9C,IAAI,EAC5BoB,KAAK,CAACsB,SAAS,CAAC1C,IAAI,EACpBoB,KAAK,CAACsC,aAAa,EACnBtC,KAAK,CAACqC,YAAY,CACrB;QAED,IAAI,CAACpB,mBAAmB,CAACW,iBAAiB,EACtC;UACIa,SAAS,EAAE,IAAI,CAAC9B,eAAe,CAACiB,iBAAiB,CAAC;UAClDc,WAAW,EAAEX,gBAAgB;UAC7BY,gBAAgB,EAAEd,eAAe,KAAK,gBAAgB,GAAG,IAAI,GAAGI,cAAc;UAC9EW,QAAQ,EAAEd,aAAa;UACvBe,aAAa,EAAEhB,eAAe,KAAK,gBAAgB,GAAG,IAAI,GAAGG,WAAW;UACxEc,aAAa,EAAE,IAAI,CAACzH,YAAY;UAChC0H,SAAS,EAAE,IAAI,CAACR,SAAS,CAACR,gBAAgB,CAAC,EAAEpF,EAAE;UAC/CqG,cAAc,EAAE,IAAI,CAACT,SAAS,CAACN,cAAc,CAAC,EAAEtF;SACnD,EACD,MAAK;UACD9C,iBAAiB,CACbmG,KAAK,CAACsB,SAAS,CAAC1C,IAAI,EACpBoB,KAAK,CAAC0B,iBAAiB,CAAC9C,IAAI,EAC5BoB,KAAK,CAACqC,YAAY,EAClBrC,KAAK,CAACsC,aAAa,CACtB;UACD,IAAI,CAACpH,aAAa,CAACwF,KAAK,CAAC,IAAI,CAAC3F,iBAAiB,CAACyC,OAAO,CAAC,kCAAkC,CAAC,CAAC;QAChG,CAAC,EAAE,MAAK;UACJ,IAAI,CAACtC,aAAa,CAACsH,OAAO,CAAC,IAAI,CAACzH,iBAAiB,CAACyC,OAAO,CAAC,sCAAsC,CAAC,CAAC;QACtG,CAAC,CAAC;;;EAGlB;EAEAyF,YAAY,CAACC,IAAY;IACrB;IACA,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,IAAI,CAAC;IAC3B,MAAMG,KAAK,GAAGF,IAAI,CAACG,QAAQ,EAAE,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACzD,MAAMC,OAAO,GAAGN,IAAI,CAACO,UAAU,EAAE,CAACH,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC7D,OAAO,GAAGH,KAAK,IAAII,OAAO,EAAE;EAChC;EAEAlB,SAAS,CAACG,WAAW;IACjB,OAAO,IAAI,CAAClE,gBAAgB,CAAC,SAAS,CAAC,CAACpC,IAAI,CAAE0E,IAAI,IAAI;MAClD,OAAOA,IAAI,CAACgC,aAAa,KAAK,CAAC,IAAI,CAACzH,YAAY,IAAIyF,IAAI,CAAC4B,WAAW,KAAK,CAACA,WAAW,IAAI5B,IAAI,CAAC6C,UAAU,KAAK,IAAI,CAACzE,YAAY;IAClI,CAAC,CAAC;EACN;EAEA0E,YAAY,CAAChD,WAAW,EAAEpF,QAAQ;IAC9B,IAAI,CAACqI,cAAc,GAAG;MAClBxI,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BI,QAAQ,EAAE,IAAI,CAAC+C,gBAAgB,CAAC,WAAW,CAAC,CAACoC,WAAW,CAAC,CAACjE,EAAE;MAC5DwG,IAAI,EAAE,IAAI,CAACjE,YAAY;MACvB1D,QAAQ;MACRsI,WAAW,EAAE,IAAI,CAACxE,WAAW,CAACsB,WAAW,CAAC,CAACmD,GAAG,CAAEjD,IAAI,IAAKA,IAAI,CAACE,YAAY;KAC7E;IACD,IAAI,CAAC/F,aAAa,CAACiF,IAAI,CAAC,IAAI,CAAC8D,iBAAiB,EAAE;MAC5C5D,QAAQ,EAAE;KACb,CAAC;EACN;EAEA6D,UAAU,CAACrD,WAAW;IAElB,MAAMxF,UAAU,GAAG,IAAI,CAACoD,gBAAgB,CAAC,WAAW,CAAC,CAACoC,WAAW,CAAC,CAACjE,EAAE;IACrE,MAAMnB,QAAQ,GAAG,IAAI,CAAC+G,SAAS,CAACnH,UAAU,CAAC,EAAEuB,EAAE;IAC/C,MAAMmH,WAAW,GAAG,IAAI,CAACxE,WAAW,CAACsB,WAAW,CAAC,CAACmD,GAAG,CAAEjD,IAAI,IAAKA,IAAI,CAACE,YAAY,CAAC;IAElFlH,IAAI,CAACoK,IAAI,CAAC;MACNC,KAAK,EAAE,IAAI,CAACpJ,iBAAiB,CAACyC,OAAO,CAAC,eAAe,CAAC;MACtD4G,IAAI,EAAE,IAAI,CAACrJ,iBAAiB,CAACyC,OAAO,CAAC,gDAAgD,CAAC;MACtF6G,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE,IAAI;MACtBC,cAAc,EAAE;KACnB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;MACf,IAAIA,MAAM,CAACC,WAAW,EAAE;QACpB,IAAI,CAAC1J,oBAAoB,CAAC2J,cAAc,CAAC,IAAI,CAACtJ,YAAY,EAAED,UAAU,EAAE0I,WAAW,EAAEtI,QAAQ,CAAC,CAACO,SAAS,CAAEC,GAAG,IAAI;UAC7G,IAAI,CAAC2B,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC;UAClC,IAAI,CAACzC,aAAa,CAACsH,OAAO,CAAC,IAAI,CAACzH,iBAAiB,CAACyC,OAAO,CAAC,gCAAgC,CAAC,CAAC;QAChG,CAAC,CAAC;;IAEV,CAAC,CAAC;EACN;EAUAoH,iBAAiB,CAACxJ,UAAU,EAAEG,cAAc,EAAEC,QAAQ;IAClDqJ,OAAO,CAACC,GAAG,CAAC,4DAA4D,EAAEtJ,QAAQ,CAAC;IACnFqJ,OAAO,CAACC,GAAG,CAAC,kEAAkE,EAAEvJ,cAAc,CAAC;IAC/F,IAAI,CAACwJ,gBAAgB,GAAG;MACpB,GAAG,IAAI,CAACA,gBAAgB;MACxB3J,UAAU;MACVC,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BE,cAAc;MACdC;KACH;IAED,IAAI,CAACP,aAAa,CAACiF,IAAI,CAAC,IAAI,CAAC8E,cAAc,EAAE;MACzC5E,QAAQ,EAAE;KAEb,CAAC;EAEN;EAEA;EACA6E,cAAc,CAACjF,KAAiB,EAAEc,IAAS;IAEvCd,KAAK,CAACkF,cAAc,EAAE;IACtBlF,KAAK,CAACmF,eAAe,EAAE;IAEvB,IAAInF,KAAK,CAACoF,MAAM,CAAC,WAAW,CAAC,KAAK,aAAa,EAAE;MAC7C;;IAGJ,MAAMC,UAAU,GAAG,IAAI,CAACC,aAAa,CAACxE,IAAI,CAAC;IAE3C;IACA,IAAI,IAAI,CAACyE,gBAAgB,KAAKF,UAAU,EAAE;MACtC,IAAI,CAACE,gBAAgB,GAAG,IAAI;KAC/B,MAAM;MACH;MACA,IAAI,CAACA,gBAAgB,GAAGF,UAAU;;EAE1C;EAEAG,cAAc,CAAC1E,IAAS;IACpB,MAAMuE,UAAU,GAAG,IAAI,CAACC,aAAa,CAACxE,IAAI,CAAC;IAC3C,OAAO,IAAI,CAACyE,gBAAgB,KAAKF,UAAU;EAC/C;EAEAC,aAAa,CAACxE,IAAS;IACnB;IACA,OAAO,GAAGA,IAAI,CAACE,YAAY,IAAIF,IAAI,CAACtE,IAAI,IAAIsE,IAAI,CAAC8B,QAAQ,IAAI,aAAa,EAAE;EAChF;EAEA6C,iBAAiB;IACb,IAAI,CAACF,gBAAgB,GAAG,IAAI;EAChC;EAGAG,eAAe,CAAC1F,KAAiB;IAC7B,MAAMoF,MAAM,GAAGpF,KAAK,CAACoF,MAAqB;IAE1C;IACA,IAAI,CAACA,MAAM,CAACO,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAACP,MAAM,CAACO,OAAO,CAAC,WAAW,CAAC,EAAE;MACnE,IAAI,CAACF,iBAAiB,EAAE;;EAEhC;EAKA;EACAG,WAAW,CAAC9E,IAAS;IACjB,IAAI,CAAC2E,iBAAiB,EAAE;IAExB,IAAI,CAACI,YAAY,GAAG/E,IAAI;IAExB,IAAI,CAAC7F,aAAa,CAACiF,IAAI,CAAC,IAAI,CAAC4F,gBAAgB,EAAE;MAC3C1F,QAAQ,EAAE;KACb,CAAC;EAEN;EAEA2F,oBAAoB,CAACjF,IAAS,EAAEtF,QAAQ;IACpC,MAAMwK,WAAW,GAAG;MAChBC,KAAK,EAAE,kDAAkD;MACzDC,KAAK,EAAE;KACV;IAED,MAAMC,cAAc,GAAG;MACnBF,KAAK,EAAE,iCAAiC;MACxCC,KAAK,EAAE;KACV;IAED,MAAME,YAAY,GAAG;MACjBH,KAAK,EAAE,6BAA6B;MACpCC,KAAK,EAAE;KACV;IAGDpM,IAAI,CAACoK,IAAI,CAAC;MACNC,KAAK,EAAE,IAAI,CAACpJ,iBAAiB,CAACyC,OAAO,CAAC,eAAe,CAAC;MACtD4G,IAAI,EAAE,IAAI,CAACrJ,iBAAiB,CAACyC,OAAO,CAACwI,WAAW,CAAClF,IAAI,CAACtE,IAAI,CAAC,CAAC;MAC5D6H,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE,IAAI;MACtBC,cAAc,EAAE,IAAI;MACpB8B,iBAAiB,EAAE,IAAI,CAACtL,iBAAiB,CAACyC,OAAO,CAAC,KAAK,CAAC;MACxD8I,gBAAgB,EAAE,IAAI,CAACvL,iBAAiB,CAACyC,OAAO,CAAC,QAAQ;KAC5D,CAAC,CAACgH,IAAI,CAAEC,MAAM,IAAI;MACf,IAAIA,MAAM,CAACC,WAAW,EAAE;QACpB,IAAI,CAACnE,eAAe,CAACO,IAAI,CAACE,YAAY,EAAExF,QAAQ,EAAE,MAAK;UACnD,IAAI,CAACN,aAAa,CAACwF,KAAK,CAAC,IAAI,CAAC3F,iBAAiB,CAACyC,OAAO,CAAC4I,YAAY,CAACtF,IAAI,CAACtE,IAAI,CAAC,CAAC,CAAC;QACrF,CAAC,EAAE,MAAK;UACJ,IAAI,CAACtB,aAAa,CAACsH,OAAO,CAAC,IAAI,CAACzH,iBAAiB,CAACyC,OAAO,CAAC2I,cAAc,CAACrF,IAAI,CAACtE,IAAI,CAAC,CAAC,CAAC;QACzF,CAAC,CAAC;;IAEV,CAAC,CAAC;IAEF,IAAI,CAACiJ,iBAAiB,EAAE;EAC5B;EAEAc,eAAe,CAACzF,IAAS,EAAEtF,QAAgB;IAEvC,IAAI,CAACiK,iBAAiB,EAAE;IAExB,IAAI,CAACV,gBAAgB,GAAG;MACpBzJ,UAAU,EAAEwF,IAAI,CAACE,YAAY;MAC7B5F,UAAU,EAAE0F,IAAI,CAAC4B,WAAW;MAC5BrH,YAAY,EAAEyF,IAAI,CAACgC,aAAa;MAChCkD,WAAW,EAAElF,IAAI,CAACkF,WAAW;MAC7BQ,cAAc,EAAE1F,IAAI,CAAC2F,eAAe;MACpCjL,QAAQ,EAAEA;KACb;IAED,IAAI,CAACP,aAAa,CAACiF,IAAI,CAAC,IAAI,CAAC8E,cAAc,EAAE;MACzC5E,QAAQ,EAAE;KACb,CAAC;EAEN;EAEAsG,aAAa;IACT5M,IAAI,CAACoK,IAAI,CAAC;MACNC,KAAK,EAAE,IAAI,CAACpJ,iBAAiB,CAACyC,OAAO,CAAC,eAAe,CAAC;MACtD4G,IAAI,EAAE,IAAI,CAACrJ,iBAAiB,CAACyC,OAAO,CAAC,sCAAsC,CAAC;MAC5E6G,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE,IAAI;MACtBC,cAAc,EAAE,IAAI;MACpB8B,iBAAiB,EAAE,IAAI,CAACtL,iBAAiB,CAACyC,OAAO,CAAC,gBAAgB,CAAC;MACnE8I,gBAAgB,EAAE,IAAI,CAACvL,iBAAiB,CAACyC,OAAO,CAAC,QAAQ;KAC5D,CAAC,CAACgH,IAAI,CAAEC,MAAM,IAAI;MACf,IAAIA,MAAM,CAACC,WAAW,EAAE;QACpB,IAAI,CAAC1J,oBAAoB,CAAC0L,aAAa,CAAC,IAAI,CAACrL,YAAY,CAAC,CAACU,SAAS,CAAEC,GAAG,IAAI;UACzElC,IAAI,CAACoK,IAAI,CAAC;YACNC,KAAK,EAAE,IAAI,CAACpJ,iBAAiB,CAACyC,OAAO,CAAC,UAAU,CAAC;YACjD4G,IAAI,EAAE,IAAI,CAACrJ,iBAAiB,CAACyC,OAAO,CAAC,4BAA4B,CAAC;YAClE6G,IAAI,EAAE;WACT,CAAC;UACF,IAAI,CAAC1G,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC;QACtC,CAAC,EAAG+C,KAAK,IAAI;UACT5G,IAAI,CAACoK,IAAI,CAAC;YACNC,KAAK,EAAE,IAAI,CAACpJ,iBAAiB,CAACyC,OAAO,CAAC,QAAQ,CAAC;YAC/C4G,IAAI,EAAE,IAAI,CAACrJ,iBAAiB,CAACyC,OAAO,CAAC,4BAA4B,CAAC;YAClE6G,IAAI,EAAE;WACT,CAAC;QACN,CAAC,CAAC;;IAEV,CAAC,CAAC;EACN;EAEAsC,kBAAkB,CAACC,eAAuB,EAAEpK,IAAkC,EAAEqK,WAAoB;IAChG,QAAQrK,IAAI;MACR,KAAK,MAAM;QACP,OAAO,IAAI,CAACqC,aAAa,CAAC+H,eAAe,CAAC,EAAExK,IAAI,CAAC0E,IAAI,IAAIA,IAAI,CAACgG,OAAO,KAAKD,WAAW,CAAC;MAC1F,KAAK,SAAS;QACV,OAAO,IAAI,CAAC9H,gBAAgB,CAAC6H,eAAe,CAAC,EAAExK,IAAI,CAAC0E,IAAI,IAAIA,IAAI,CAACiG,UAAU,KAAKF,WAAW,CAAC;MAChG,KAAK,OAAO;QACR,OAAO,IAAI,CAAChI,aAAa,CAAC+H,eAAe,CAAC,IAAI,IAAI,CAAC7H,gBAAgB,CAAC6H,eAAe,CAAC;IAAC;EAEjG;EAEAI,WAAW;IACP,IAAI,CAACpK,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;IAC1B,IAAI,CAAC5B,oBAAoB,CAACiM,8BAA8B,CAAC,IAAI,CAAC5L,YAAY,CAAC,CAACU,SAAS,CAAEC,GAAG,IAAI;MAC1F,IAAI,CAACd,aAAa,CAACsH,OAAO,CAACxG,GAAG,CAACkL,OAAO,CAAC;IAC3C,CAAC,EAAGxG,KAAK,IAAI;MAET,IAAI,CAAC9D,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;MAC1B,IAAI,CAAC1B,aAAa,CAACwF,KAAK,CAACA,KAAK,CAACwG,OAAO,CAAC;IAC3C,CAAC,CAAC;EACN;EAEAC,QAAQ,CAACC,SAAS;IACd,IAAI,CAAC3B,iBAAiB,EAAE;IACxB,IAAI,CAAC7K,aAAa,CAACyM,SAAS,CAACD,SAAS,CAAC,CAACrL,SAAS,CAAEC,GAAG,IAAI;MACtD,IAAI,CAACd,aAAa,CAACsH,OAAO,CAAC,0BAA0B,CAAC;MACtD,IAAI,CAAC7E,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC;IACtC,CAAC,EAAG+C,KAAK,IAAI;MACT,IAAI,CAACxF,aAAa,CAACwF,KAAK,CAACA,KAAK,CAACwG,OAAO,IAAI,uBAAuB,CAAC;IACtE,CAAC,CAAC;EACN;EAAKI,YAAY;IACb,IAAI,CAAC1M,aAAa,CAAC2M,mBAAmB,CAAC,IAAI,CAACrL,oBAAoB,CAAC,CAACH,SAAS,CAAEC,GAAG,IAAI;MAChF,IAAI,CAACd,aAAa,CAACsH,OAAO,CAAC,qCAAqC,CAAC;MACjE,IAAI,CAAC7E,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC;IACtC,CAAC,EAAG+C,KAAK,IAAI;MACT5G,IAAI,CAACoK,IAAI,CAAC;QACNC,KAAK,EAAE,sBAAsB;QAC7BE,IAAI,EAAE3D,KAAK,CAAC8G,OAAO,GAAG,SAAS,GAAG,OAAO;QACzCpD,IAAI,EAAE1D,KAAK,CAAC8G,OAAO,IAAI9G,KAAK,CAACwG,OAAO,IAAI,kCAAkC;QAC1Eb,iBAAiB,EAAE;OACtB,CAAC,CAAC7B,IAAI,CAAEC,MAAM,IAAI;QACf,IAAIA,MAAM,CAACC,WAAW,EAAE;UACpB,IAAI,CAAChK,OAAO,CAAC+M,QAAQ,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,CAACpM,YAAY,EAAE,QAAQ,EAAE,IAAI,CAACa,oBAAoB,CAAC,CAAC;;MAE5G,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EAEA;;;;;;EAMQwL,sBAAsB,CAACC,QAAe,EAAEC,OAAe;IAC3D,IAAI,IAAI,CAACC,sBAAsB,CAACC,GAAG,CAACF,OAAO,CAAC,EAAE;MAC1C,OAAOD,QAAQ,CAACxJ,MAAM,CAAC,CAAC;;IAG5B;IACA,MAAM4J,eAAe,GAAGJ,QAAQ,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAI;MACjD,MAAMhL,IAAI,GAAGgL,GAAG,CAACC,YAAY,KAAK,MAAM,GAClC,GAAGD,GAAG,CAACE,IAAI,EAAEC,UAAU,IAAI,EAAE,IAAIH,GAAG,CAACE,IAAI,EAAEE,SAAS,IAAI,EAAE,EAAE,CAACC,IAAI,EAAE,GACnEL,GAAG,CAACM,YAAY,IAAI,EAAE;MAC5B,OAAOP,GAAG,GAAG/K,IAAI,CAACiB,MAAM;IAC5B,CAAC,EAAE,CAAC,CAAC;IAEL,MAAMsK,aAAa,GAAGV,eAAe,GAAGJ,QAAQ,CAACxJ,MAAM;IAEvD;IACA,OAAOsK,aAAa,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC;EACrC;EAEA;;;;;;EAMAC,oBAAoB,CAACf,QAAe,EAAEC,OAAe;IACjD,IAAI,CAACD,QAAQ,IAAIA,QAAQ,CAACxJ,MAAM,KAAK,CAAC,EAAE;MACpC,OAAO,EAAE;;IAGb,MAAMwK,KAAK,GAAG,IAAI,CAACjB,sBAAsB,CAACC,QAAQ,EAAEC,OAAO,CAAC;IAC5D,OAAOD,QAAQ,CAACiB,KAAK,CAAC,CAAC,EAAED,KAAK,CAAC;EACnC;EAEA;;;;;;EAMAE,yBAAyB,CAAClB,QAAe,EAAEC,OAAe;IACtD,IAAI,CAACD,QAAQ,IAAIA,QAAQ,CAACxJ,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC0J,sBAAsB,CAACC,GAAG,CAACF,OAAO,CAAC,EAAE;MAChF,OAAO,CAAC;;IAGZ,MAAMe,KAAK,GAAG,IAAI,CAACjB,sBAAsB,CAACC,QAAQ,EAAEC,OAAO,CAAC;IAC5D,OAAOkB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEpB,QAAQ,CAACxJ,MAAM,GAAGwK,KAAK,CAAC;EAC/C;EAEA;;;;;EAKAK,sBAAsB,CAACpB,OAAe,EAAE5H,KAAY;IAChDA,KAAK,CAACmF,eAAe,EAAE;IACvBnF,KAAK,CAACkF,cAAc,EAAE;IAEtB,MAAM+D,SAAS,GAAG,IAAI,CAACC,aAAa,CAACtB,OAAO,CAAC;IAC7C,IAAI,CAACqB,SAAS,IAAI,CAACA,SAAS,CAACtB,QAAQ,IAAIsB,SAAS,CAACtB,QAAQ,CAACxJ,MAAM,KAAK,CAAC,EAAE;MACtE;;IAGJ;IACA,IAAI8K,SAAS,CAACtB,QAAQ,CAACxJ,MAAM,GAAG,CAAC,EAAE;MAC/B,IAAI,CAACgL,gBAAgB,CAACF,SAAS,CAACtB,QAAQ,EAAEC,OAAO,CAAC;KACrD,MAAM;MACH,IAAI,IAAI,CAACC,sBAAsB,CAACC,GAAG,CAACF,OAAO,CAAC,EAAE;QAC1C,IAAI,CAACC,sBAAsB,CAACuB,MAAM,CAACxB,OAAO,CAAC;OAC9C,MAAM;QACH,IAAI,CAACC,sBAAsB,CAACwB,GAAG,CAACzB,OAAO,CAAC;;;EAGpD;EAEA;;;;;EAKQuB,gBAAgB,CAACxB,QAAe,EAAEC,OAAe;IACrD,IAAI,CAAC0B,qBAAqB,GAAG3B,QAAQ;IACrC,IAAI,CAAC4B,eAAe,GAAG3B,OAAO;IAE9B,IAAI,CAAC3M,aAAa,CAACiF,IAAI,CAAC,IAAI,CAACsJ,gBAAgB,EAAE;MAC3CpJ,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE;KACT,CAAC;EACN;EAEA;;;;;EAKQ6I,aAAa,CAACtB,OAAe;IACjC,KAAK,MAAMhH,WAAW,IAAI,IAAI,CAACvB,eAAe,EAAE;MAC5C,MAAMoK,OAAO,GAAG,IAAI,CAACnK,WAAW,CAACsB,WAAW,CAAC,IAAI,EAAE;MACnD,MAAMqF,KAAK,GAAGwD,OAAO,CAACrN,IAAI,CAAC0E,IAAI,IAAIA,IAAI,CAACnE,EAAE,KAAKiL,OAAO,CAAC;MACvD,IAAI3B,KAAK,EAAE;QACP,OAAOA,KAAK;;;IAIpB;IACA,OAAO,IAAI,CAAC7H,sBAAsB,CAAChC,IAAI,CAAC0E,IAAI,IAAIA,IAAI,CAACnE,EAAE,KAAKiL,OAAO,CAAC,IAAI,IAAI;EAChF;EAAC;qBArtBQrN,qBAAqB;EAAA;EAAA;UAArBA,qBAAqB;IAAAmP;IAAAC;MAAA;;;;;;;;;;;;;;;;;;;iBAArBC,2BAAuB;QAAA;;;;;;;;QCvBpC5P,8BAA+C;QAG3CA,wCAAyE;QACzEA,8BAAwB;QAIMA,YAAwB;;QAAAA,iBAAQ;QACxDA,oCAE2B;QADqBA;UAAA;QAAA,EAA0B;UAAA,OAAW4P,wBAAoB;QAAA,EAA/B;;QAExE5P,oFACY;QACdA,iBAAY;QAEdA,+BAAwD;QACtBA;UAAA,OAAS4P,4BAAwB;QAAA,EAAC;QAChE5P,yBAA6C;QAC7CA,+BACF;QAAAA,iBAAS;QACTA,mCAGK;QAHwBA;UAAA,OAAS4P,iBAAa;QAAA,EAAC;QAIlD5P,yBAA0E;QAE5EA,iBAAS;QACTA,mCAA8F;QAA9CA;UAAA,OAAS4P,mBAAe;QAAA,EAAC;QACvE5P,yBAA8B;QAEhCA,iBAAS;QAIfA,gCAAsB;QAElBA,yEA0JM;QACNA,2FAEe;QACfA,2FAEe;QACjBA,iBAAM;QACNA,gCAA2C;QAIrBA,4BAAW;QAAAA,iBAAI;QAC7BA,8BAAiB;QACfA,yFAEF;QAAAA,iBAAI;QAENA,gCAC0D;QAAxDA;UAAA,OAAsB4P,gBAAY;QAAA,EAAC;QACnC5P,2FAWe;QACfA,2FAWe;QACjBA,iBAAM;QAOlBA,0HAGc;QACdA,0HAEc;QACdA,0HAEc;QAEdA,0HAEc;QAEdA,2HA4Bc;QAEdA,0HAac;QAEdA,0HASc;QAEdA,2HAqBc;QAEdA,4HAwEc;;;QArZUA,eAA+B;QAA/BA,iDAA+B;QAKrBA,eAAwB;QAAxBA,mDAAwB;QAE9CA,eAA6C;QAA7CA,8EAA6C;QADpBA,iCAAmB;QAGhBA,eAAc;QAAdA,yCAAc;QAKuBA,eAAmB;QAAnBA,qCAAmB;QAIjCA,eAGjD;QAHiDA,8EAGjD;QACCA,eAAkE;QAAlEA,oFAAkE;QAGGA,eAAmB;QAAnBA,qCAAmB;QASrDA,eAAa;QAAbA,kCAAa;QA2JxCA,eAAwC;QAAxCA,qEAAwC;QAGxCA,eAAgB;QAAhBA,qCAAgB;QAcOA,eAA0C;QAA1CA,4DAA0C;QAE3DA,eAAgB;QAAhBA,qCAAgB;QAYhBA,eAAiB;QAAjBA,sCAAiB", "names": ["moveItemInArray", "transferArrayItem", "<PERSON><PERSON>", "AppConfig", "i0", "ctx_r30", "ctx_r47", "ctx_r50", "ctx_r54", "ctx_r57", "ctx_r59", "AutoScheduleComponent", "constructor", "_route", "_router", "_tournamentService", "_stageService", "_loadingService", "_titleService", "_translateService", "_autoScheduleService", "_modalService", "_toastService", "Set", "locationId", "tournamentId", "timeSlotId", "lastTimeSlotId", "configId", "location", "isFetching", "snapshot", "paramMap", "get", "getTournament", "subscribe", "res", "tournamentInfo", "leagueOrGroupStageId", "stages", "find", "stage", "TOURNAMENT_TYPES", "groups_knockouts", "type", "groups", "league", "id", "isLock", "is_locked_schedule", "seasonId", "group", "season", "setTitle", "name", "contentHeader", "headerTitle", "actionButton", "breadcrumb", "links", "instant", "isLink", "link", "onScheduleAction", "ngOnInit", "ngAfterViewChecked", "setTimeout", "hasMatches", "responseMatches", "Object", "keys", "length", "listUnScheduledMatches", "getScheduleMatches", "showLoading", "show", "responseMetadata", "metadata", "Array", "isArray", "data", "teamConflicts", "conflicts", "referee<PERSON><PERSON><PERSON><PERSON><PERSON>", "stageConflicts", "dateOptions", "selectedDate", "includes", "mapListLocations", "listLocationIds", "listMatches", "dismiss", "hasPlan", "getUnScheduleMatches", "getListUnScheduledMatches", "listLocations", "for<PERSON>ach", "locationName", "push", "onSelectDate", "event", "openModalSetupSchedule", "open", "modalSetupSchedule", "centered", "size", "response", "unScheduleMatch", "onError", "onSuccess", "error", "mapNewSlotIndex", "locationKey", "newSlotIndex", "item", "index", "time_slot_id", "updateLocationMatch", "updateData", "newIndex", "drop", "targetContainer", "container", "element", "nativeElement", "prevContainer", "previousContainer", "dragItem", "targetContainerId", "prevContainerId", "targetStageId", "targetLocationId", "prevStageId", "prevLocationId", "itemStageId", "itemTimeSlotId", "itemType", "currentIndex", "previousIndex", "getConfig", "success", "new_index", "location_id", "prev_location_id", "stage_id", "prev_stage_id", "tournament_id", "config_id", "prev_config_id", "getShortTime", "time", "date", "Date", "hours", "getHours", "toString", "padStart", "minutes", "getMinutes", "begin_date", "editSchedule", "selectedConfig", "timeSlotIds", "map", "modalEditSchedule", "deletePlan", "fire", "title", "text", "icon", "showCancelButton", "reverseButtons", "then", "result", "isConfirmed", "deleteSchedule", "openModalAddBreak", "console", "log", "breakModalParams", "modalCrudBreak", "toggleDropdown", "preventDefault", "stopPropagation", "target", "dropdownId", "getDropdownId", "activeDropdownId", "isDropdownOpen", "closeAllDropdowns", "onDocumentClick", "closest", "onEditMatch", "selectedItem", "modalUpdateMatch", "onUnscheduleTimeSlot", "description", "match", "break", "successMessage", "errorMessage", "confirmButtonText", "cancelButtonText", "onEditEventTime", "breakDurations", "break_durations", "clearSchedule", "isMatchHasConflict", "scheduleMatchId", "itemCheckId", "team_id", "referee_id", "onClickLock", "updateTournamentScheduleStatus", "message", "swapTeam", "matchInfo", "swapTeams", "autoGenerate", "autoGenerateMatches", "warning", "navigate", "getRefereeDisplayLimit", "referees", "matchId", "expandedRefereeMatches", "has", "totalNameLength", "reduce", "sum", "ref", "referee_type", "user", "first_name", "last_name", "trim", "referee_name", "averageLength", "getDisplayedReferees", "limit", "slice", "getRemainingRefereesCount", "Math", "max", "toggleRefereeExpansion", "matchItem", "findMatchById", "showRefereeModal", "delete", "add", "selectedMatchReferees", "selectedMatchId", "modalRefereeList", "matches", "selectors", "viewQuery", "ctx"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\auto-schedule\\auto-schedule.component.ts", "D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\auto-schedule\\auto-schedule.component.html"], "sourcesContent": ["import { Component, HostListener, TemplateRef, ViewChild } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { TournamentService } from '../../../services/tournament.service';\r\nimport { LoadingService } from '../../../services/loading.service';\r\nimport { Title } from '@angular/platform-browser';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { AutoScheduleService, UpdateLocationMatchParams } from '../../../services/auto-schedule.service';\r\nimport { CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport Swal from 'sweetalert2';\r\nimport { BreakModalParams } from \"./modal-crud-break/modal-crud-break.component\";\r\nimport { ToastrService } from \"ngx-toastr\";\r\nimport { AppConfig } from \"../../../app-config\";\r\nimport { StageService } from \"../../../services/stage.service\";\r\nimport { UpdateConfigParams } from './modal-update-config/modal-update-config.component';\r\nimport config from '../../../../../capacitor.config';\r\n\r\n\r\n@Component({\r\n    selector: 'app-auto-schedule',\r\n    templateUrl: './auto-schedule.component.html',\r\n    styleUrls: ['./auto-schedule.component.scss']\r\n})\r\nexport class AutoScheduleComponent {\r\n\r\n    public contentHeader: object;\r\n    public tournamentId: any;\r\n    public seasonId: any;\r\n    public leagueOrGroupStageId: number | null = null;\r\n    public tournamentInfo: null;\r\n\r\n    public dateOptions = [];\r\n    public listLocations = {};\r\n    public listMatches = {};\r\n    public listLocationIds = [];\r\n    public responseMetadata = {};\r\n    public responseMatches: {} | null = {}\r\n\r\n    public teamConflicts = {};\r\n    public refereeConflicts = {};\r\n    public stageConflicts = {};\r\n\r\n\r\n    public selectedConfig: UpdateConfigParams | null = null;\r\n\r\n    public isFetching = true;\r\n    public isLock = false;\r\n    public hasMatches = false;\r\n\r\n\r\n    @ViewChild('modalSetupSchedule')\r\n    modalSetupSchedule!: TemplateRef<any>;\r\n\r\n    @ViewChild('modalEditSchedule')\r\n    modalEditSchedule!: TemplateRef<any>;\r\n\r\n    @ViewChild('modalCrudBreak')\r\n    modalCrudBreak!: TemplateRef<any>;    @ViewChild('modalUpdateMatch')\r\n    modalUpdateMatch: TemplateRef<any>;\r\n\r\n    @ViewChild('modalRefereeList')\r\n    modalRefereeList: TemplateRef<any>;\r\n\r\n    // public listUnScheduledMatches = [\r\n\r\n    // ];\r\n\r\n    public listUnScheduledMatches = [];\r\n\r\n    public selectedDate = null;\r\n\r\n    public hasPlan = false;\r\n\r\n    // Simple dropdown state - track which dropdown is open\r\n    public activeDropdownId: string | null = null;\r\n\r\n    // Referee expansion state\r\n    public expandedRefereeMatches: Set<number> = new Set();\r\n    public selectedMatchReferees: any[] = [];\r\n    public selectedMatchId: number | null = null;\r\n\r\n    constructor(\r\n        public _route: ActivatedRoute,\r\n        public _router: Router,\r\n        public _tournamentService: TournamentService,\r\n        public _stageService: StageService,\r\n        public _loadingService: LoadingService,\r\n        public _titleService: Title,\r\n        public _translateService: TranslateService,\r\n        public _autoScheduleService: AutoScheduleService,\r\n        private _modalService: NgbModal,\r\n        private _toastService: ToastrService,\r\n    ) {\r\n        this.isFetching = true;\r\n        this.tournamentId = this._route.snapshot.paramMap.get('tournament_id');\r\n        this._tournamentService.getTournament(this.tournamentId).subscribe(\r\n            (res) => {\r\n                this.tournamentInfo = res;\r\n\r\n                this.leagueOrGroupStageId = res.stages.find((stage) => {\r\n                    if (this.tournamentInfo && this.tournamentInfo['type'] === AppConfig.TOURNAMENT_TYPES.groups_knockouts) {\r\n                        return stage.type === AppConfig.TOURNAMENT_TYPES.groups;\r\n                    } else {\r\n                        return stage.type === AppConfig.TOURNAMENT_TYPES.league;\r\n                    }\r\n                })?.id;\r\n\r\n                this.isLock = res.is_locked_schedule === 1;\r\n                this.seasonId = res.group.season.id;\r\n                _titleService.setTitle(res.name);\r\n\r\n                this.contentHeader = {\r\n                    headerTitle: res.name,\r\n                    actionButton: false,\r\n                    breadcrumb: {\r\n                        type: '',\r\n                        links: [\r\n                            {\r\n                                name: this._translateService.instant('Leagues'),\r\n                                isLink: false\r\n                            },\r\n                            {\r\n                                name: this._translateService.instant('Manage Leagues'),\r\n                                isLink: true,\r\n                                link: '/leagues/manage'\r\n                            },\r\n                            {\r\n                                name: res.name,\r\n                                isLink: false\r\n                            },\r\n                            {\r\n                                name: this._translateService.instant('Auto Schedule'),\r\n                                isLink: false\r\n                            }\r\n                        ]\r\n                    }\r\n                };\r\n\r\n                this.onScheduleAction(null, true)\r\n            });\r\n    }\r\n\r\n    ngOnInit() {\r\n    }\r\n\r\n    ngAfterViewChecked() {\r\n        setTimeout(() => {\r\n            this.hasMatches = (this.responseMatches && Object.keys(this.responseMatches).length > 0) || this.listUnScheduledMatches.length > 0;\r\n        }, 0)\r\n    }\r\n\r\n    getScheduleMatches(showLoading: boolean = true) {\r\n        if (showLoading) {\r\n            this._loadingService.show();\r\n        }\r\n\r\n\r\n        this._autoScheduleService.getScheduleMatches(this.tournamentId)\r\n            .subscribe((res) => {\r\n                this.responseMetadata = res.metadata;\r\n                this.responseMatches = Array.isArray(res.data) && res.data.length === 0 ? null : res.data;\r\n\r\n                this.teamConflicts = res.conflicts['team_scheduling_conflict'];\r\n                this.refereeConflicts = res.conflicts['referee_scheduling_conflict'];\r\n                this.stageConflicts = res.conflicts['stage_scheduling_conflict'];\r\n\r\n                if (this.responseMatches) {\r\n                    this.dateOptions = Object.keys(this.responseMatches);\r\n                    this.selectedDate = this.selectedDate && this.dateOptions.includes(this.selectedDate)\r\n                        ? this.selectedDate\r\n                        : this.dateOptions[0];\r\n                    this.mapListLocations();\r\n                } else {\r\n                    this.listLocationIds = [];\r\n                    this.listMatches = {};\r\n                    this.dateOptions = [];\r\n                    this.selectedDate = null;\r\n                }\r\n\r\n                this._loadingService.dismiss();\r\n            }, () => {\r\n            }, () => {\r\n                this.isFetching = false\r\n                this.hasPlan = this.dateOptions.length > 0;\r\n            });\r\n    }\r\n\r\n    getUnScheduleMatches(showLoading = true) {\r\n        if (showLoading) {\r\n            this._loadingService.show();\r\n        }\r\n        this._autoScheduleService.getListUnScheduledMatches(this.tournamentId)\r\n            .subscribe((res) => {\r\n                this.listUnScheduledMatches = res.data;\r\n\r\n                this._loadingService.dismiss();\r\n            });\r\n    }\r\n\r\n    mapListLocations() {\r\n        this.listLocationIds = [];\r\n        this.listMatches = {};\r\n\r\n        this.listLocations = this.responseMatches[this.selectedDate] || {};\r\n\r\n        if (!this.listLocations || !this.selectedDate) return;\r\n\r\n        Object.keys(this.listLocations).forEach((locationName: string) => {\r\n\r\n            if (!this.listLocationIds.includes(`${locationName}`)) {\r\n                this.listLocationIds.push(`${locationName}`);\r\n            }\r\n\r\n            if (!this.listMatches[locationName]) {\r\n                this.listMatches[locationName] = [];\r\n            }\r\n\r\n            this.listMatches[locationName] = [\r\n                ...this.listMatches[locationName],\r\n                ...this.listLocations[locationName]\r\n            ]\r\n        });\r\n\r\n    }\r\n\r\n    onSelectDate(event) {\r\n        this.selectedDate = event;\r\n        this.mapListLocations();\r\n    }\r\n\r\n    openModalSetupSchedule() {\r\n        this._modalService.open(this.modalSetupSchedule, {\r\n            centered: true,\r\n            size: 'lg'\r\n        });\r\n    }\r\n\r\n    onScheduleAction(response, showLoading: boolean = true) {\r\n        this.getScheduleMatches(showLoading);\r\n        this.getUnScheduleMatches(showLoading);\r\n    }\r\n\r\n    unScheduleMatch(timeSlotId: string | number, configId: string | number, onError: () => void, onSuccess: () => void) {\r\n        this._autoScheduleService.unScheduleMatch(timeSlotId, configId).subscribe((res) => {\r\n            this.onScheduleAction(null, false);\r\n            onSuccess && onSuccess()\r\n        }, (error) => {\r\n            onError();\r\n        })\r\n    }\r\n\r\n\r\n    mapNewSlotIndex(locationKey) {\r\n\r\n        const newSlotIndex = {};\r\n        this.listMatches[locationKey].forEach((item, index) => {\r\n\r\n            newSlotIndex[item.time_slot_id] = index;\r\n        });\r\n        return newSlotIndex;\r\n    }\r\n\r\n    updateLocationMatch(locationKey: string, updateData: UpdateLocationMatchParams, onError: () => void, onSuccess: () => void) {\r\n        const newIndex = this.mapNewSlotIndex(locationKey);\r\n        this._autoScheduleService.updateLocationMatch(updateData).subscribe((res) => {\r\n            this.onScheduleAction(null, false);\r\n            onSuccess && onSuccess();\r\n        }, (error) => {\r\n            onError();\r\n        });\r\n    }\r\n\r\n\r\n    drop(event: CdkDragDrop<string[]>) {\r\n        const targetContainer = event.container.element.nativeElement;\r\n        const prevContainer = event.previousContainer.element.nativeElement;\r\n        const dragItem = event.item.element.nativeElement;\r\n\r\n        const targetContainerId = targetContainer['id'];\r\n        const prevContainerId = prevContainer['id'];\r\n\r\n        const targetStageId = targetContainer['data_stage_id'];\r\n        const targetLocationId = targetContainer['data_location_id'];\r\n\r\n        const prevStageId = prevContainer['data_stage_id'];\r\n        const prevLocationId = prevContainer['data_location_id'];\r\n\r\n        const itemStageId = dragItem['data_stage_id'];\r\n        const itemTimeSlotId = dragItem['data_time_slot_id'];\r\n        const itemType = dragItem['data_type'];\r\n\r\n        if (prevContainerId === targetContainerId && event.currentIndex === event.previousIndex) return;\r\n\r\n        if (targetContainerId === 'unScheduleZone') {\r\n            transferArrayItem(\r\n                event.previousContainer.data,\r\n                event.container.data,\r\n                event.previousIndex,\r\n                event.currentIndex\r\n            );\r\n\r\n            if (targetContainerId === prevContainerId) return;\r\n\r\n            this.unScheduleMatch(itemTimeSlotId, this.getConfig(prevLocationId)?.id, () => {\r\n                transferArrayItem(\r\n                    event.container.data,\r\n                    event.previousContainer.data,\r\n                    event.currentIndex,\r\n                    event.previousIndex\r\n                );\r\n                this._toastService.error(this._translateService.instant('Failed to unschedule match.'));\r\n            }, () => {\r\n                this._toastService.success(this._translateService.instant('Match unscheduled successfully.'));\r\n            });\r\n        } else {\r\n            if (event.previousContainer === event.container) {\r\n                moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);\r\n                this.updateLocationMatch(targetContainerId, {\r\n                    new_index: this.mapNewSlotIndex(targetContainerId),\r\n                    location_id: targetLocationId,\r\n                    prev_location_id: prevLocationId,\r\n                    stage_id: targetStageId,\r\n                    prev_stage_id: prevStageId,\r\n                    tournament_id: this.tournamentId,\r\n                    config_id: this.getConfig(targetLocationId)?.id,\r\n                    prev_config_id: this.getConfig(prevLocationId)?.id,\r\n                }, () => {\r\n                    moveItemInArray(event.container.data, event.currentIndex, event.previousIndex);\r\n                    this._toastService.error(this._translateService.instant('Failed to update match schedule.'));\r\n                }, () => {\r\n                    this._toastService.success(this._translateService.instant('Match schedule updated successfully.'));\r\n                });\r\n            } else {\r\n                transferArrayItem(\r\n                    event.previousContainer.data,\r\n                    event.container.data,\r\n                    event.previousIndex,\r\n                    event.currentIndex\r\n                )\r\n\r\n                this.updateLocationMatch(targetContainerId,\r\n                    {\r\n                        new_index: this.mapNewSlotIndex(targetContainerId),\r\n                        location_id: targetLocationId,\r\n                        prev_location_id: prevContainerId === 'unScheduleZone' ? null : prevLocationId,\r\n                        stage_id: targetStageId,\r\n                        prev_stage_id: prevContainerId === 'unScheduleZone' ? null : prevStageId,\r\n                        tournament_id: this.tournamentId,\r\n                        config_id: this.getConfig(targetLocationId)?.id,\r\n                        prev_config_id: this.getConfig(prevLocationId)?.id,\r\n                    },\r\n                    () => {\r\n                        transferArrayItem(\r\n                            event.container.data,\r\n                            event.previousContainer.data,\r\n                            event.currentIndex,\r\n                            event.previousIndex\r\n                        );\r\n                        this._toastService.error(this._translateService.instant('Failed to update match schedule.'));\r\n                    }, () => {\r\n                        this._toastService.success(this._translateService.instant('Match schedule updated successfully.'));\r\n                    });\r\n            }\r\n        }\r\n    }\r\n\r\n    getShortTime(time: string): string {\r\n        // handle return ISO string to short time format HH:mm and format 24 hours\r\n        if (!time) return '';\r\n        const date = new Date(time);\r\n        const hours = date.getHours().toString().padStart(2, '0');\r\n        const minutes = date.getMinutes().toString().padStart(2, '0');\r\n        return `${hours}:${minutes}`;\r\n    }\r\n\r\n    getConfig(location_id) {\r\n        return this.responseMetadata['configs'].find((item) => {\r\n            return item.tournament_id === +this.tournamentId && item.location_id === +location_id && item.begin_date === this.selectedDate;\r\n        });\r\n    }\r\n\r\n    editSchedule(locationKey, configId) {\r\n        this.selectedConfig = {\r\n            tournamentId: this.tournamentId,\r\n            location: this.responseMetadata['locations'][locationKey].id,\r\n            date: this.selectedDate,\r\n            configId,\r\n            timeSlotIds: this.listMatches[locationKey].map((item) => item.time_slot_id),\r\n        };\r\n        this._modalService.open(this.modalEditSchedule, {\r\n            centered: true,\r\n        });\r\n    }\r\n\r\n    deletePlan(locationKey) {\r\n\r\n        const locationId = this.responseMetadata['locations'][locationKey].id;\r\n        const configId = this.getConfig(locationId)?.id;\r\n        const timeSlotIds = this.listMatches[locationKey].map((item) => item.time_slot_id);\r\n\r\n        Swal.fire({\r\n            title: this._translateService.instant('Are you sure?'),\r\n            text: this._translateService.instant('You will not be able to recover this schedule!'),\r\n            icon: 'warning',\r\n            showCancelButton: true,\r\n            reverseButtons: true\r\n        }).then((result) => {\r\n            if (result.isConfirmed) {\r\n                this._autoScheduleService.deleteSchedule(this.tournamentId, locationId, timeSlotIds, configId).subscribe((res) => {\r\n                    this.onScheduleAction(null, false);\r\n                    this._toastService.success(this._translateService.instant('Schedule deleted successfully.'));\r\n                });\r\n            }\r\n        });\r\n    }\r\n\r\n    public breakModalParams: BreakModalParams = {\r\n        locationId: null,\r\n        tournamentId: null,\r\n        timeSlotId: null,\r\n        lastTimeSlotId: null,\r\n        configId: null\r\n    }\r\n\r\n    openModalAddBreak(locationId, lastTimeSlotId, configId) {\r\n        console.log(\"🚀 ~ AutoScheduleComponent ~ openModalAddBreak ~ configId:\", configId)\r\n        console.log(\"🚀 ~ AutoScheduleComponent ~ openModalAddBreak ~ lastTimeSlotId:\", lastTimeSlotId)\r\n        this.breakModalParams = {\r\n            ...this.breakModalParams,\r\n            locationId,\r\n            tournamentId: this.tournamentId,\r\n            lastTimeSlotId,\r\n            configId\r\n        }\r\n\r\n        this._modalService.open(this.modalCrudBreak, {\r\n            centered: true,\r\n\r\n        });\r\n\r\n    }\r\n\r\n    // Simple dropdown methods\r\n    toggleDropdown(event: MouseEvent, item: any): void {\r\n\r\n        event.preventDefault();\r\n        event.stopPropagation();\r\n\r\n        if (event.target['className'] === 'swap-button') {\r\n            return;\r\n        }\r\n\r\n        const dropdownId = this.getDropdownId(item);\r\n\r\n        // Close dropdown if clicking on the same item\r\n        if (this.activeDropdownId === dropdownId) {\r\n            this.activeDropdownId = null;\r\n        } else {\r\n            // Open new dropdown (close any existing one)\r\n            this.activeDropdownId = dropdownId;\r\n        }\r\n    }\r\n\r\n    isDropdownOpen(item: any): boolean {\r\n        const dropdownId = this.getDropdownId(item);\r\n        return this.activeDropdownId === dropdownId;\r\n    }\r\n\r\n    getDropdownId(item: any): string {\r\n        // Create unique ID for each item\r\n        return `${item.time_slot_id}_${item.type}_${item.stage_id || 'unscheduled'}`;\r\n    }\r\n\r\n    closeAllDropdowns(): void {\r\n        this.activeDropdownId = null;\r\n    }\r\n\r\n    @HostListener('document:click', ['$event'])\r\n    onDocumentClick(event: MouseEvent): void {\r\n        const target = event.target as HTMLElement;\r\n\r\n        // Close dropdown if clicking outside of dropdown or dnd-item\r\n        if (!target.closest('.item-dropdown') && !target.closest('.dnd-item')) {\r\n            this.closeAllDropdowns();\r\n        }\r\n    }\r\n\r\n\r\n    public selectedItem = null;\r\n\r\n    // Dropdown action handlers\r\n    onEditMatch(item: any): void {\r\n        this.closeAllDropdowns();\r\n\r\n        this.selectedItem = item;\r\n\r\n        this._modalService.open(this.modalUpdateMatch, {\r\n            centered: true,\r\n        });\r\n\r\n    }\r\n\r\n    onUnscheduleTimeSlot(item: any, configId): void {\r\n        const description = {\r\n            match: 'This match will be moved to unscheduled matches.',\r\n            break: 'This break will be removed.'\r\n        }\r\n\r\n        const successMessage = {\r\n            match: 'Match unscheduled successfully.',\r\n            break: 'Break removed successfully.'\r\n        }\r\n\r\n        const errorMessage = {\r\n            match: 'Failed to unschedule match.',\r\n            break: 'Failed to remove break.'\r\n        }\r\n\r\n\r\n        Swal.fire({\r\n            title: this._translateService.instant('Are you sure?'),\r\n            text: this._translateService.instant(description[item.type]),\r\n            icon: 'warning',\r\n            showCancelButton: true,\r\n            reverseButtons: true,\r\n            confirmButtonText: this._translateService.instant('Yes'),\r\n            cancelButtonText: this._translateService.instant('Cancel')\r\n        }).then((result) => {\r\n            if (result.isConfirmed) {\r\n                this.unScheduleMatch(item.time_slot_id, configId, () => {\r\n                    this._toastService.error(this._translateService.instant(errorMessage[item.type]));\r\n                }, () => {\r\n                    this._toastService.success(this._translateService.instant(successMessage[item.type]));\r\n                });\r\n            }\r\n        });\r\n\r\n        this.closeAllDropdowns();\r\n    }\r\n\r\n    onEditEventTime(item: any, configId: number): void {\r\n\r\n        this.closeAllDropdowns();\r\n\r\n        this.breakModalParams = {\r\n            timeSlotId: item.time_slot_id,\r\n            locationId: item.location_id,\r\n            tournamentId: item.tournament_id,\r\n            description: item.description,\r\n            breakDurations: item.break_durations,\r\n            configId: configId,\r\n        }\r\n\r\n        this._modalService.open(this.modalCrudBreak, {\r\n            centered: true,\r\n        })\r\n\r\n    }\r\n\r\n    clearSchedule() {\r\n        Swal.fire({\r\n            title: this._translateService.instant('Are you sure?'),\r\n            text: this._translateService.instant('This action will clear all schedule.'),\r\n            icon: 'warning',\r\n            showCancelButton: true,\r\n            reverseButtons: true,\r\n            confirmButtonText: this._translateService.instant('Yes, clear it!'),\r\n            cancelButtonText: this._translateService.instant('Cancel')\r\n        }).then((result) => {\r\n            if (result.isConfirmed) {\r\n                this._autoScheduleService.clearSchedule(this.tournamentId).subscribe((res) => {\r\n                    Swal.fire({\r\n                        title: this._translateService.instant('Success!'),\r\n                        text: this._translateService.instant('Schedule has been cleared.'),\r\n                        icon: 'success'\r\n                    });\r\n                    this.onScheduleAction(null, false);\r\n                }, (error) => {\r\n                    Swal.fire({\r\n                        title: this._translateService.instant('Error!'),\r\n                        text: this._translateService.instant('Schedule has been cleared.'),\r\n                        icon: 'success'\r\n                    });\r\n                })\r\n            }\r\n        });\r\n    }\r\n\r\n    isMatchHasConflict(scheduleMatchId: number, type: 'team' | 'referee' | 'match', itemCheckId?: number) {\r\n        switch (type) {\r\n            case 'team':\r\n                return this.teamConflicts[scheduleMatchId]?.find(item => item.team_id === itemCheckId);\r\n            case 'referee':\r\n                return this.refereeConflicts[scheduleMatchId]?.find(item => item.referee_id === itemCheckId);\r\n            case 'match':\r\n                return this.teamConflicts[scheduleMatchId] || this.refereeConflicts[scheduleMatchId];\r\n        }\r\n    }\r\n\r\n    onClickLock() {\r\n        this.isLock = !this.isLock;\r\n        this._autoScheduleService.updateTournamentScheduleStatus(this.tournamentId).subscribe((res) => {\r\n            this._toastService.success(res.message)\r\n        }, (error) => {\r\n\r\n            this.isLock = !this.isLock;\r\n            this._toastService.error(error.message)\r\n        });\r\n    }\r\n\r\n    swapTeam(matchInfo) {\r\n        this.closeAllDropdowns();\r\n        this._stageService.swapTeams(matchInfo).subscribe((res) => {\r\n            this._toastService.success('Swap teams successfully.');\r\n            this.onScheduleAction(null, false);\r\n        }, (error) => {\r\n            this._toastService.error(error.message || 'Failed to swap teams.');\r\n        })\r\n    }    autoGenerate() {\r\n        this._stageService.autoGenerateMatches(this.leagueOrGroupStageId).subscribe((res) => {\r\n            this._toastService.success('Auto generate matches successfully.');\r\n            this.onScheduleAction(null, false);\r\n        }, (error) => {\r\n            Swal.fire({\r\n                title: 'Cannot Auto Generate',\r\n                icon: error.warning ? 'warning' : 'error',\r\n                text: error.warning || error.message || 'Failed to auto generate matches.',\r\n                confirmButtonText: 'Go to Stage Details',\r\n            }).then((result) => {\r\n                if (result.isConfirmed) {\r\n                    this._router.navigate(['leagues', 'manage', this.tournamentId, 'stages', this.leagueOrGroupStageId]);\r\n                }\r\n            });\r\n        })\r\n    }\r\n\r\n    /**\r\n     * Get the display limit for referees based on their name lengths\r\n     * @param referees - Array of referees\r\n     * @param matchId - Match ID for expansion state\r\n     * @returns Maximum number of referees to show\r\n     */\r\n    private getRefereeDisplayLimit(referees: any[], matchId: number): number {\r\n        if (this.expandedRefereeMatches.has(matchId)) {\r\n            return referees.length; // Show all if expanded\r\n        }\r\n\r\n        // Calculate average name length\r\n        const totalNameLength = referees.reduce((sum, ref) => {\r\n            const name = ref.referee_type === 'user' \r\n                ? `${ref.user?.first_name || ''} ${ref.user?.last_name || ''}`.trim()\r\n                : ref.referee_name || '';\r\n            return sum + name.length;\r\n        }, 0);\r\n        \r\n        const averageLength = totalNameLength / referees.length;\r\n        \r\n        // If average name length is long (>15 chars), show 3; otherwise show 4\r\n        return averageLength > 15 ? 3 : 4;\r\n    }\r\n\r\n    /**\r\n     * Get the list of referees to display (limited or all based on expansion state)\r\n     * @param referees - Array of all referees\r\n     * @param matchId - Match ID for expansion state\r\n     * @returns Array of referees to display\r\n     */\r\n    getDisplayedReferees(referees: any[], matchId: number): any[] {\r\n        if (!referees || referees.length === 0) {\r\n            return [];\r\n        }\r\n\r\n        const limit = this.getRefereeDisplayLimit(referees, matchId);\r\n        return referees.slice(0, limit);\r\n    }\r\n\r\n    /**\r\n     * Get the count of remaining referees not shown\r\n     * @param referees - Array of all referees\r\n     * @param matchId - Match ID for expansion state\r\n     * @returns Number of remaining referees\r\n     */\r\n    getRemainingRefereesCount(referees: any[], matchId: number): number {\r\n        if (!referees || referees.length === 0 || this.expandedRefereeMatches.has(matchId)) {\r\n            return 0;\r\n        }\r\n\r\n        const limit = this.getRefereeDisplayLimit(referees, matchId);\r\n        return Math.max(0, referees.length - limit);\r\n    }\r\n\r\n    /**\r\n     * Toggle referee expansion for a specific match\r\n     * @param matchId - Match ID\r\n     * @param event - Click event\r\n     */\r\n    toggleRefereeExpansion(matchId: number, event: Event): void {\r\n        event.stopPropagation();\r\n        event.preventDefault();\r\n\r\n        const matchItem = this.findMatchById(matchId);\r\n        if (!matchItem || !matchItem.referees || matchItem.referees.length === 0) {\r\n            return;\r\n        }\r\n\r\n        // If there are many referees (>6), show modal; otherwise toggle inline expansion\r\n        if (matchItem.referees.length > 6) {\r\n            this.showRefereeModal(matchItem.referees, matchId);\r\n        } else {\r\n            if (this.expandedRefereeMatches.has(matchId)) {\r\n                this.expandedRefereeMatches.delete(matchId);\r\n            } else {\r\n                this.expandedRefereeMatches.add(matchId);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Show modal with all referees for a match\r\n     * @param referees - Array of all referees\r\n     * @param matchId - Match ID\r\n     */\r\n    private showRefereeModal(referees: any[], matchId: number): void {\r\n        this.selectedMatchReferees = referees;\r\n        this.selectedMatchId = matchId;\r\n        \r\n        this._modalService.open(this.modalRefereeList, {\r\n            centered: true,\r\n            size: 'md'\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Find a match by its ID across all locations\r\n     * @param matchId - Match ID to search for\r\n     * @returns Match item or null if not found\r\n     */\r\n    private findMatchById(matchId: number): any {\r\n        for (const locationKey of this.listLocationIds) {\r\n            const matches = this.listMatches[locationKey] || [];\r\n            const match = matches.find(item => item.id === matchId);\r\n            if (match) {\r\n                return match;\r\n            }\r\n        }\r\n\r\n        // Also check unscheduled matches\r\n        return this.listUnScheduledMatches.find(item => item.id === matchId) || null;\r\n    }\r\n\r\n    protected readonly location = location;\r\n\r\n}\r\n", "<div class=\"content-wrapper container-xxl p-0\">\r\n  <div class=\"content-body\">\r\n    <!-- content-header component -->\r\n    <app-content-header [contentHeader]=\"contentHeader\"></app-content-header>\r\n    <div class=\"top-header\">\r\n      <div class=\"row mb-1\">\r\n        <!-- ng select season -->\r\n        <div class=\"d-flex flex-column\" style=\"gap: 2px\">\r\n          <label for=\"selectDate\">{{ 'Date' | translate }}</label>\r\n          <ng-select id=\"selectDate\" [searchable]=\"true\" [clearable]=\"false\"\r\n            placeholder=\"{{ 'Select Date' | translate }}\" [(ngModel)]=\"selectedDate\" (change)=\"onSelectDate($event)\"\r\n            style=\"min-width: 200px\">\r\n            <ng-option *ngFor=\"let date of dateOptions\" [value]=\"date\">{{ date }}\r\n            </ng-option>\r\n          </ng-select>\r\n        </div>\r\n        <div class=\"d-flex align-items-center\" style=\"gap: 8px\">\r\n          <button class=\"btn btn-primary\" (click)=\"openModalSetupSchedule()\" [disabled]=\"isLock\">\r\n            <i data-feather=\"settings\" class=\"mr-25\"></i>\r\n            Add Schedule\r\n          </button>\r\n          <button class=\"btn btn-icon\" (click)=\"onClickLock()\" [ngClass]=\"{\r\n              'btn-outline-primary': !isLock,\r\n              'btn-primary': isLock\r\n            }\">\r\n            <i [ngClass]=\"isLock ? 'fa-regular fa-lock' : 'fa-regular fa-unlock'\"></i>\r\n            <!--                        {{ isLock ? 'Unlock' : 'Lock' }}-->\r\n          </button>\r\n          <button class=\"btn btn-icon btn-outline-danger\" (click)=\"clearSchedule()\" [disabled]=\"isLock\">\r\n            <i data-feather=\"trash-2\"></i>\r\n            <!--                        Clear Schedule-->\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div cdkDropListGroup>\r\n      <div class=\"col-9\" id=\"listLocationZone\">\r\n        <div class=\"horizontal-scroll-container\" *ngIf=\"hasPlan\">\r\n          <div class=\"location-columns-wrapper\">\r\n            <ng-container *ngFor=\"let locationKey of listLocationIds\">\r\n              <div class=\"location-column mb-2\">\r\n                <div class=\"bg-white shadow-sm\">\r\n                  <header class=\"location-header\">\r\n                    <div class=\"d-flex align-items-start justify-content-between\">\r\n                      <p class=\"location-name h4\">\r\n                        {{ locationKey }}\r\n                      </p>\r\n                      <div class=\"\" ngbDropdown *ngIf=\"!isLock\">\r\n                        <button class=\"btn btn-link dropdown-toggle\" ngbDropdownToggle>\r\n                          <i class=\"fa-solid fa-ellipsis-vertical\"></i>\r\n                        </button>\r\n                        <div ngbDropdownMenu aria-labelledby=\"dropdownMenuButton\">\r\n                          <a ngbDropdownItem (click)=\"\r\n                              editSchedule(\r\n                                locationKey,\r\n                                getConfig(\r\n                                  responseMetadata['locations'][locationKey].id\r\n                                )?.id\r\n                              )\r\n                            \">\r\n                            Edit Plan\r\n                          </a>\r\n                          <a ngbDropdownItem (click)=\"deletePlan(locationKey)\">\r\n                            Delete Plan\r\n                          </a>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    <p class=\"location-stage-name\">\r\n                      {{\r\n                      getShortTime(\r\n                      getConfig(\r\n                      responseMetadata['locations'][locationKey].id\r\n                      )?.begin_date +\r\n                      ' ' +\r\n                      getConfig(\r\n                      responseMetadata['locations'][locationKey].id\r\n                      )?.begin_time || ''\r\n                      )\r\n                      }}\r\n                    </p>\r\n                  </header>\r\n                  <div class=\"dnd-zone\" cdkDropList [cdkDropListData]=\"listMatches[locationKey]\"\r\n                    (cdkDropListDropped)=\"drop($event)\" [id]=\"locationKey\" [data_location_id]=\"\r\n                      responseMetadata['locations'][locationKey].id\r\n                    \">\r\n                    <div *ngFor=\"let item of listMatches[locationKey]\" class=\"dnd-item location-match-row\" [ngClass]=\"{\r\n                        'conflict-border': isMatchHasConflict(item.id, 'match')\r\n                      }\" cdkDrag [data_stage_id]=\"item.stage_id\" [data_time_slot_id]=\"item.time_slot_id\"\r\n                      [data_type]=\"item.type\" [cdkDragDisabled]=\"isLock\"\r\n                      (click)=\"!isLock && toggleDropdown($event, item)\">\r\n                      <button type=\"button\" rippleEffect class=\"conflict-tooltip btn btn-link\" placement=\"right\"\r\n                        container=\"body\" ngbTooltip=\"This match has conflict\"\r\n                        *ngIf=\"isMatchHasConflict(item.id, 'match')\">\r\n                        <i class=\"fa-light fa-circle-exclamation\" style=\"font-size: 16px\"></i>\r\n                      </button>\r\n\r\n                      <ng-container *ngIf=\"item.type === 'match'\">\r\n                        <ng-container *ngTemplateOutlet=\"\r\n                            matchScheduledTemplate;\r\n                            context: { $implicit: item }\r\n                          \"></ng-container>\r\n                      </ng-container>\r\n                      <ng-container *ngIf=\"item.type === 'break'\">\r\n                        <div class=\"break-info-header\">\r\n                          <p class=\"text-center m-0\">\r\n                            {{ item.description || 'Break time' }}\r\n                          </p>\r\n                        </div>\r\n                        <div class=\"break-row\">\r\n                          <i class=\"fa-regular fa-clock\" aria-hidden=\"true\"></i>\r\n                          <p class=\"break-time m-0\">\r\n                            {{ item.break_durations }} mins\r\n                          </p>\r\n                        </div>\r\n                      </ng-container>\r\n\r\n                      <!-- Individual Dropdown for this item -->\r\n                      <div class=\"item-dropdown\" [class.visible]=\"isDropdownOpen(item)\">\r\n                        <div class=\"dropdown-content\">\r\n                          <!-- Match type dropdown options -->\r\n                          <ng-container *ngIf=\"item.type === 'match'\">\r\n                            <button class=\"dropdown-item\" (click)=\"onEditMatch(item)\">\r\n                              <i class=\"fa-regular fa-whistle mr-2\" style=\"rotate: -45deg\"></i>\r\n                              {{ 'Update Match Referees' | translate }}\r\n                            </button>\r\n                            <button class=\"dropdown-item\" (click)=\"\r\n                                onUnscheduleTimeSlot(\r\n                                  item,\r\n                                  getConfig(\r\n                                    responseMetadata['locations'][locationKey]\r\n                                      .id\r\n                                  )?.id\r\n                                )\r\n                              \">\r\n                              <i class=\"fa fa-trash mr-2\"></i>\r\n                              {{ 'Unschedule Match' | translate }}\r\n                            </button>\r\n                          </ng-container>\r\n\r\n                          <!-- Break type dropdown options -->\r\n                          <ng-container *ngIf=\"item.type === 'break'\">\r\n                            <button class=\"dropdown-item\" (click)=\"\r\n                                onEditEventTime(\r\n                                  item,\r\n                                  getConfig(\r\n                                    responseMetadata['locations'][locationKey]\r\n                                      .id\r\n                                  )?.id\r\n                                )\r\n                              \">\r\n                              <i class=\"fa fa-clock mr-2\"></i>\r\n                              {{ 'Edit Event Time' | translate }}\r\n                            </button>\r\n                            <button class=\"dropdown-item\" (click)=\"\r\n                                onUnscheduleTimeSlot(\r\n                                  item,\r\n                                  getConfig(\r\n                                    responseMetadata['locations'][locationKey]\r\n                                      .id\r\n                                  )?.id\r\n                                )\r\n                              \">\r\n                              <i class=\"fa fa-trash mr-2\"></i>\r\n                              {{ 'Delete Event' | translate }}\r\n                            </button>\r\n                          </ng-container>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <footer class=\"location-footer\" *ngIf=\"!isLock\">\r\n                    <button class=\"btn btn-link w-100\" (click)=\"\r\n                        openModalAddBreak(\r\n                          responseMetadata['locations'][locationKey].id,\r\n                          listMatches[locationKey][\r\n                            listMatches[locationKey].length - 1\r\n                          ]?.time_slot_id,\r\n                          getConfig(\r\n                            responseMetadata['locations'][locationKey].id\r\n                          )?.id\r\n                        )\r\n                      \">\r\n                      <i class=\"fa fa-plus\" aria-hidden=\"true\"></i>\r\n                      Add event / break\r\n                    </button>\r\n                  </footer>\r\n                </div>\r\n              </div>\r\n            </ng-container>\r\n          </div>\r\n        </div>\r\n        <ng-container *ngIf=\"!hasPlan && !isFetching && !isLock\">\r\n          <ng-container *ngTemplateOutlet=\"noSchedule\"></ng-container>\r\n        </ng-container>\r\n        <ng-container *ngIf=\"isFetching\">\r\n          <ng-container *ngTemplateOutlet=\"fetchingState\"></ng-container>\r\n        </ng-container>\r\n      </div>\r\n      <div id=\"listUnScheduleZone\" class=\"col-3\">\r\n        <div class=\"unschedule-container\">\r\n          <div class=\"location-column unplanned-matches-container\">\r\n            <div class=\"location-header\">\r\n              <p class=\"h4\">Not Planned</p>\r\n              <p class=\"small\">\r\n                You can add unscheduled matches to the calendar by drag and drop\r\n                them.\r\n              </p>\r\n            </div>\r\n            <div class=\"dnd-zone\" cdkDropList [cdkDropListData]=\"listUnScheduledMatches\"\r\n              (cdkDropListDropped)=\"drop($event)\" id=\"unScheduleZone\">\r\n              <ng-container *ngIf=\"hasMatches\">\r\n                <div *ngFor=\"let item of listUnScheduledMatches\" class=\"dnd-item location-match-row\" cdkDrag\r\n                  [data_stage_id]=\"item.stage_id\" [data_time_slot_id]=\"item.time_slot_id\" [data_type]=\"item.type\"\r\n                  [cdkDragDisabled]=\"isLock\">\r\n                  <ng-container *ngIf=\"item.type === 'match'\">\r\n                    <ng-container *ngTemplateOutlet=\"\r\n                        matchNotScheduledTemplate;\r\n                        context: { $implicit: item }\r\n                      \"></ng-container>\r\n                  </ng-container>\r\n                </div>\r\n              </ng-container>\r\n              <ng-container *ngIf=\"!hasMatches\">\r\n                <div id=\"notHaveMatches\">\r\n                  <p class=\"text-center\">\r\n                    No matches found for this tournament.\r\n                  </p>\r\n\r\n                  <button (click)=\"autoGenerate()\" class=\"btn btn-primary\">\r\n                    <i class=\"fa-solid fa-wand-magic-sparkles\"></i>\r\n                    Auto Generate\r\n                  </button>\r\n                </div>\r\n              </ng-container>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n<ng-template #modalSetupSchedule let-modal>\r\n  <app-modal-setup-schedule [seasonId]=\"seasonId\" [tournamentId]=\"tournamentId\" [tournamentInfo]=\"tournamentInfo\"\r\n    (onSubmit)=\"onScheduleAction($event)\" />\r\n</ng-template>\r\n<ng-template #modalEditSchedule let-modal>\r\n  <app-modal-update-config [selectedConfig]=\"selectedConfig\" (onSubmit)=\"onScheduleAction($event)\" />\r\n</ng-template>\r\n<ng-template #modalCrudBreak let-modal>\r\n  <app-modal-crud-break [breakModalParams]=\"breakModalParams\" (onSubmit)=\"onScheduleAction($event)\" />\r\n</ng-template>\r\n\r\n<ng-template #modalUpdateMatch let-modal>\r\n  <app-modal-update-match [timeSlotInfo]=\"selectedItem\" [seasonId]=\"seasonId\" (onSubmit)=\"onScheduleAction($event)\" />\r\n</ng-template>\r\n\r\n<ng-template #modalRefereeList let-modal>\r\n  <div class=\"modal-header\">\r\n    <h4 class=\"modal-title\" id=\"modal-title\">{{ 'Match Referees' | translate }}</h4>\r\n    <button type=\"button\" class=\"btn-close\" aria-label=\"Close\" (click)=\"modal.dismiss()\">\r\n      <span aria-hidden=\"true\">&times;</span>\r\n    </button>\r\n  </div>\r\n  <div class=\"modal-body\">\r\n    <div class=\"referee-list\">\r\n      <div class=\"referee-item\" *ngFor=\"let ref of selectedMatchReferees\">\r\n        <div class=\"referee-info\">\r\n          <span *ngIf=\"ref.referee_type == 'user'\" [ngClass]=\"{\r\n              'text-danger': isMatchHasConflict(selectedMatchId, 'referee', ref.id)\r\n            }\">\r\n            {{ ref.user?.first_name }} {{ ref.user?.last_name }}\r\n          </span>\r\n          <span *ngIf=\"ref.referee_type == 'freetext'\" [ngClass]=\"{\r\n              'text-danger': isMatchHasConflict(selectedMatchId, 'referee', ref.id)\r\n            }\">\r\n            {{ ref.referee_name }}\r\n          </span>\r\n          <small class=\"referee-type\">{{ ref.referee_type === 'user' ? 'Registered User' : 'External Referee' }}</small>\r\n        </div>\r\n        <i *ngIf=\"isMatchHasConflict(selectedMatchId, 'referee', ref.id)\"\r\n          class=\"fa-solid fa-exclamation-triangle text-danger\" title=\"This referee has a conflict\"></i>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</ng-template>\r\n\r\n<ng-template #noSchedule>\r\n  <div id=\"noSchedule\">\r\n    <div class=\"col d-flex flex-column align-items-center justify-content-center g-2\" style=\"height: 500px\">\r\n      <p class=\"h5\">No Plan Created</p>\r\n      <p style=\"color: rgba(168, 170, 174, 1)\" class=\"w-75 text-center\">\r\n        Please enter the necessary information to allow the system to generate\r\n        an accurate schedule based on your requirements.\r\n      </p>\r\n      <button class=\"btn btn-primary\" (click)=\"openModalSetupSchedule()\">\r\n        Setup\r\n      </button>\r\n    </div>\r\n  </div>\r\n</ng-template>\r\n\r\n<ng-template #fetchingState>\r\n  <div id=\"fetchingState\">\r\n    <div class=\"col d-flex flex-column align-items-center justify-content-center g-2\" style=\"height: 500px\">\r\n      <p class=\"h5\">Fetching</p>\r\n      <p style=\"color: rgba(168, 170, 174, 1)\" class=\"w-75 text-center\">\r\n        Waiting for getting schedule data...\r\n      </p>\r\n    </div>\r\n  </div>\r\n</ng-template>\r\n\r\n<ng-template #matchNotScheduledTemplate let-item>\r\n  <div class=\"match-info-header\">\r\n    <p class=\"text-center m-0\">{{ item.match.round_name }}</p>\r\n  </div>\r\n  <div class=\"team-row\">\r\n    <div class=\"home-team\">\r\n      <img src=\"https://assets.codepen.io/285131/whufc.svg\" alt=\"Home Team Logo\" class=\"team-logo\" />\r\n      <span class=\"h6 team-name\">{{\r\n        item.match?.home_team?.name ?? 'TBD'\r\n        }}</span>\r\n    </div>\r\n    <button class=\"swap-button\" (click)=\"swapTeam(item.match)\">\r\n      <i class=\"fa fa-right-left\" aria-hidden=\"true\"></i>\r\n    </button>\r\n    <div class=\"away-team\">\r\n      <span class=\"h6 team-name\">{{\r\n        item.match?.away_team?.name ?? 'TBD'\r\n        }}</span>\r\n      <img src=\"https://assets.codepen.io/285131/whufc.svg\" alt=\"Away Team Logo\" class=\"team-logo\" />\r\n    </div>\r\n  </div>\r\n</ng-template>\r\n\r\n<ng-template #matchScheduledTemplate let-item>\r\n  <div class=\"match-info-header\">\r\n    <p class=\"text-center m-0\">\r\n      {{ item.match.round_name }}\r\n    </p>\r\n  </div>\r\n  <div class=\"team-row\">\r\n    <div class=\"home-team\">\r\n      <img src=\"https://assets.codepen.io/285131/whufc.svg\" alt=\"Home Team Logo\" class=\"team-logo\" />\r\n      <span class=\"h6 team-name\" [ngClass]=\"{\r\n          'text-danger': isMatchHasConflict(\r\n            item.id,\r\n            'team',\r\n            item.match.home_team_id\r\n          )\r\n        }\">\r\n        {{ item.match.home_team?.name ?? 'TBD' }}\r\n      </span>\r\n    </div>\r\n    <button class=\"swap-button\" (click)=\"swapTeam(item.match)\">\r\n      <i class=\"fa fa-right-left\" aria-hidden=\"true\"></i>\r\n    </button>\r\n    <div class=\"away-team\">\r\n      <span class=\"h6 team-name\" [ngClass]=\"{\r\n          'text-danger': isMatchHasConflict(\r\n            item.id,\r\n            'team',\r\n            item.match.away_team_id\r\n          )\r\n        }\">\r\n        {{ item.match.away_team?.name ?? 'TBD' }}\r\n      </span>\r\n      <img src=\"https://assets.codepen.io/285131/whufc.svg\" alt=\"Away Team Logo\" class=\"team-logo\" />\r\n    </div>\r\n  </div>\r\n  <div class=\"match-date\">\r\n    <i class=\"fa-regular fa-clock\" aria-hidden=\"true\"></i>\r\n    <p class=\"text-center m-0\">\r\n      {{ getShortTime(item.start_time) }}\r\n    </p>\r\n    <p class=\"text-center m-0\">-</p>\r\n    <p class=\"text-center m-0\">\r\n      {{ getShortTime(item.end_time) }}\r\n    </p>\r\n  </div>\r\n  <div class=\"referees-row\" *ngIf=\"item.referees && item.referees.length > 0\">\r\n    <i class=\"fa-regular fa-whistle\" style=\"rotate: -45deg\"></i>\r\n\r\n    <!-- Display limited referees (3-4 based on name length) -->\r\n    <div class=\"referee-names\" *ngFor=\"let ref of getDisplayedReferees(item.referees, item.id); let last = last\">\r\n      <div class=\"referee-name\">\r\n        <span *ngIf=\"ref.referee_type == 'user'\" [ngClass]=\"{\r\n            'text-danger': isMatchHasConflict(item.id, 'referee', ref.id)\r\n          }\">\r\n          {{ ref.user?.first_name }} {{ ref.user?.last_name }}\r\n        </span>\r\n        <span *ngIf=\"ref.referee_type == 'freetext'\" [ngClass]=\"{\r\n            'text-danger': isMatchHasConflict(item.id, 'referee', ref.id)\r\n          }\">\r\n          {{ ref.referee_name }}\r\n        </span>\r\n        <span *ngIf=\"!last\">-</span>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Show remaining count if there are more referees -->\r\n    <div class=\"referee-more\" *ngIf=\"getRemainingRefereesCount(item.referees, item.id) > 0\">\r\n      <span class=\"referee-expand-btn\" (click)=\"toggleRefereeExpansion(item.id, $event)\">\r\n        +{{ getRemainingRefereesCount(item.referees, item.id) }} more\r\n      </span>\r\n    </div>\r\n  </div>\r\n</ng-template>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}