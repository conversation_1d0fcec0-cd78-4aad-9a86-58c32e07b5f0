<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Example - example-expression-one-time-production</title>
  

  <script src="//code.angularjs.org/1.6.9/angular.min.js"></script>
  <script src="script.js"></script>
  

  
</head>
<body ng-app="oneTimeBindingExampleApp">
  <div ng-controller="EventController">
  <button ng-click="clickMe($event)">Click Me</button>
  <p id="one-time-binding-example">One time binding: {{::name}}</p>
  <p id="normal-binding-example">Normal binding: {{name}}</p>
</div>
</body>
</html>